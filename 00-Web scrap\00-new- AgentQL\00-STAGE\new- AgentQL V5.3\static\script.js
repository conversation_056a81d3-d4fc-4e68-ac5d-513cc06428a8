// متغيرات عامة
let currentResults = [];
let totalExtractedResults = [];  // إجمالي المستخرج - يبقى حتى إغلاق التطبيق
let filteredResults = [];
let excludedResults = [];
let includedResults = [];
let isLoading = false;
let isScrapingActive = false;
let scrapingAborted = false;
let scrapingPaused = false;
let currentScrapingData = null;
let currentFilterTerm = '';
let controlCheckInterval = null;

// إحصائيات العداد اللحظي
let scrapingStats = {
    moviesCount: 0,
    seriesCount: 0,
    totalCount: 0,
    currentPage: 0,
    totalPages: 0,
    percentage: 0
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // ربط الأحداث الأساسية
    document.getElementById('scrapeBtn').addEventListener('click', startScraping);
    document.getElementById('debugBtn').addEventListener('click', debugQuery);
    document.getElementById('analyzeBtn').addEventListener('click', analyzePages);

    // ربط أحداث التصدير الجديدة
    document.getElementById('exportSingleFile').addEventListener('click', () => exportResults('single'));
    document.getElementById('exportMultipleFiles').addEventListener('click', () => exportResults('multiple'));
    document.getElementById('exportSettings').addEventListener('click', showExportSettings);
    document.getElementById('applyExportSettings').addEventListener('click', applyExportSettings);
    document.getElementById('selectFolderBtn').addEventListener('click', selectExportFolder);

    // ربط أحداث نظام التبديل
    document.getElementById('singlePageMode').addEventListener('change', toggleScrapingMode);
    document.getElementById('multiPageMode').addEventListener('change', toggleScrapingMode);

    // إضافة مستمعين للتغييرات في الحقول
    document.getElementById('query').addEventListener('input', updateQueryPreview);
    document.getElementById('url').addEventListener('input', validateUrl);

    // ربط أحداث سجل التطور
    document.getElementById('clearLogBtn').addEventListener('click', clearLog);
    document.getElementById('scrollToBottomBtn').addEventListener('click', scrollLogToBottom);

    // ربط أحداث أزرار التحكم في العمليات
    document.getElementById('pauseScrapingBtn').addEventListener('click', pauseScraping);
    document.getElementById('resumeScrapingBtn').addEventListener('click', resumeScraping);
    document.getElementById('stopAndExportBtn').addEventListener('click', stopAndExportScraping);

    // ربط أحداث تصفية النتائج المحدثة
    document.getElementById('excludeByNameBtn').addEventListener('click', () => filterByNameWithScroll('exclude'));
    document.getElementById('includeByNameBtn').addEventListener('click', () => filterByNameWithScroll('include'));
    document.getElementById('applyImageFilterBtn').addEventListener('click', applyImageFilterWithScroll);
    document.getElementById('resetFiltersBtn').addEventListener('click', resetAllFilters);
    document.getElementById('showAllResultsBtn').addEventListener('click', showAllResults);
    document.getElementById('showExcludedBtn').addEventListener('click', () => showFilteredResults('excluded'));
    document.getElementById('showIncludedBtn').addEventListener('click', () => showFilteredResults('included'));
    document.getElementById('exportFilteredBtn').addEventListener('click', exportFilteredResults);

    // تهيئة الوضع الافتراضي
    toggleScrapingMode();

    // تحديث معاينة الاستعلام الافتراضي
    updateQueryPreview();

    // إضافة مستمعي الأحداث للكلمات الافتراضية
    setupDefaultKeywordsListeners();

    // إضافة رسالة ترحيب
    addLogEntry('info', '🎬 مرحباً! التطبيق جاهز لاستخراج بيانات الأفلام');
    addLogEntry('info', '✨ الوضع الافتراضي: صفحات متتالية مع كلمات افتراضية للفلترة');
}

// وظيفة التحكم في الأقسام القابلة للطي
function toggleSection(sectionId) {
    const content = document.getElementById(sectionId + '-content');
    const icon = document.getElementById(sectionId + '-icon');

    if (!content || !icon) return;

    if (content.style.display === 'none' || content.style.display === '') {
        // إظهار القسم
        content.style.display = 'block';
        content.classList.add('show');
        content.classList.remove('hide');
        icon.classList.add('rotated');
    } else {
        // إخفاء القسم
        content.classList.add('hide');
        content.classList.remove('show');
        icon.classList.remove('rotated');

        // إخفاء القسم بعد انتهاء الأنيميشن
        setTimeout(() => {
            if (content.classList.contains('hide')) {
                content.style.display = 'none';
            }
        }, 300);
    }
}

// وظيفة التصفية بالاسم مع الانتقال التلقائي للإحصائيات
function filterByNameWithScroll(filterType) {
    filterByName(filterType);

    // الانتقال التلقائي إلى قسم الإحصائيات العامة
    setTimeout(() => {
        scrollToGeneralStats();
    }, 500);
}

// وظيفة الانتقال إلى قسم الإحصائيات العامة
function scrollToGeneralStats() {
    const generalStatsElement = document.getElementById('generalStatsInfo');
    if (generalStatsElement && generalStatsElement.style.display !== 'none') {
        generalStatsElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // إضافة تأثير بصري للفت الانتباه
        generalStatsElement.style.animation = 'pulse 1s ease-in-out';
        setTimeout(() => {
            generalStatsElement.style.animation = '';
        }, 1000);
    }
}

// وظيفة التصفية بالصور مع الانتقال التلقائي للإحصائيات
function applyImageFilterWithScroll() {
    applyImageFilter();

    // الانتقال التلقائي إلى قسم الإحصائيات العامة
    setTimeout(() => {
        scrollToGeneralStats();
    }, 500);
}

// وظيفة استخراج اسم الموقع من URL
function extractSiteName(url) {
    try {
        if (!url) return 'unknown_site';

        const urlObj = new URL(url);
        let hostname = urlObj.hostname;

        // إزالة www. إذا كانت موجودة
        hostname = hostname.replace(/^www\./, '');

        // إزالة النطاق العلوي (.com, .net, .org, إلخ)
        const parts = hostname.split('.');
        let siteName = parts.length > 1 ? parts[0] : hostname;

        // تنظيف اسم الموقع ليكون مناسباً لاسم الملف
        siteName = siteName
            .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_') // استبدال الرموز الخاصة بـ _
            .replace(/_+/g, '_') // دمج عدة _ متتالية
            .replace(/^_|_$/g, ''); // إزالة _ من البداية والنهاية

        return siteName || 'unknown_site';
    } catch (error) {
        console.warn('خطأ في استخراج اسم الموقع:', error);
        return 'unknown_site';
    }
}

// وظيفة الحصول على اسم الموقع الحالي
function getCurrentSiteName() {
    const scrapingMode = document.querySelector('input[name="scrapingMode"]:checked').value;

    if (scrapingMode === 'single') {
        const url = document.getElementById('url').value.trim();
        return extractSiteName(url);
    } else {
        // في حالة الصفحات المتتالية، استخدم الصفحة الأولى
        const page1 = document.getElementById('page1').value.trim();
        return extractSiteName(page1);
    }
}

// وظائف العداد اللحظي
function showProgressStats() {
    const progressStats = document.getElementById('progressStats');
    if (progressStats) {
        progressStats.style.display = 'block';
    }
}

function hideProgressStats() {
    const progressStats = document.getElementById('progressStats');
    if (progressStats) {
        progressStats.style.display = 'none';
    }
}

function updateProgressStats(stats) {
    // تحديث الأرقام مع تأثير بصري
    const elements = {
        moviesCount: document.getElementById('moviesCount'),
        seriesCount: document.getElementById('seriesCount'),
        totalCount: document.getElementById('totalCount'),
        currentPage: document.getElementById('currentPage')
    };

    Object.keys(elements).forEach(key => {
        if (elements[key] && stats[key] !== undefined) {
            const oldValue = parseInt(elements[key].textContent) || 0;
            const newValue = stats[key];

            if (newValue !== oldValue) {
                elements[key].textContent = newValue;
                elements[key].classList.add('updated');
                setTimeout(() => {
                    elements[key].classList.remove('updated');
                }, 600);
            }
        }
    });

    // تحديث شريط التقدم
    updateProgressBar(stats.percentage || 0, stats.status || 'جاري العمل...');
}

function updateProgressBar(percentage, status) {
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = document.getElementById('progressPercentage');
    const progressStatus = document.getElementById('progressStatus');

    if (progressFill && percentage !== null) {
        progressFill.style.width = `${percentage}%`;
    }

    if (progressPercentage && percentage !== null) {
        progressPercentage.textContent = `${Math.round(percentage)}%`;
    }

    if (progressStatus && status) {
        progressStatus.textContent = status;
    }

    // التأكد من أن أزرار التحكم تبقى نشطة
    enableControlButtons();
}

function resetProgressStats() {
    scrapingStats = {
        moviesCount: 0,
        seriesCount: 0,
        totalCount: 0,
        currentPage: 0,
        totalPages: 0,
        percentage: 0
    };

    updateProgressStats(scrapingStats);
    updateProgressBar(0, 'جاهز للبدء');
    hideProgressStats();
}

// وظائف التحكم في العمليات (تم استبدالها بـ updateScrapingControlButtons)

// تم نقل وظائف التحكم في العمليات إلى نهاية الملف

async function startScraping() {
    if (isLoading) return;

    // إعداد العملية
    isScrapingActive = true;
    scrapingAborted = false;
    scrapingPaused = false;
    resetProgressStats();
    showProgressStats();
    updateScrapingControlButtons(true);

    // الانتقال التلقائي لسجل التطور اللحظي
    scrollToProgressLog();

    const scrapingMode = document.querySelector('input[name="scrapingMode"]:checked').value;

    try {
        // بدء العملية في الخادم
        const success = await startScrapingOnServer();

        if (success) {
            // بدء مراقبة حالة العملية
            startStatusMonitoring();
        } else {
            throw new Error('فشل في بدء عملية الاستخراج');
        }

    } catch (error) {
        addLogEntry('error', `❌ خطأ في بدء العملية: ${error.message}`);
        showAlert(`خطأ في بدء العملية: ${error.message}`, 'danger');
        resetScrapingState();
    }
}

async function startScrapingOnServer() {
    const scrapingMode = document.querySelector('input[name="scrapingMode"]:checked').value;

    let requestData = {
        query: document.getElementById('query').value.trim(),
        max_items_per_file: parseInt(document.getElementById('maxItemsPerFile').value) || 100,
        page_load_delay: parseFloat(document.getElementById('pageLoadDelay').value) || 2,
        scraping_mode: scrapingMode
    };

    if (scrapingMode === 'single') {
        const url = document.getElementById('url').value.trim();
        if (!url) {
            addLogEntry('error', '❌ يرجى إدخال الرابط');
            showAlert('يرجى إدخال الرابط', 'danger');
            return false;
        }
        requestData.url = url;

        addLogEntry('info', '🚀 بدء عملية استخراج بيانات الأفلام والمسلسلات (صفحة منفردة)...');
        addLogEntry('info', `🔗 الموقع المستهدف: ${url}`);

    } else {
        const maxPages = parseInt(document.getElementById('maxPages').value) || 1;
        const startPage = parseInt(document.getElementById('startFromPage').value) || 1;
        const patternInfo = getPatternInfo();

        if (!patternInfo) {
            addLogEntry('error', '❌ يرجى تحليل الصفحات أولاً');
            showAlert('يرجى تحليل الصفحات أولاً', 'danger');
            return false;
        }

        requestData.max_pages = maxPages;
        requestData.start_page = startPage;
        requestData.pattern_info = patternInfo;

        addLogEntry('info', '🚀 بدء عملية الاستخراج المتتالي...');
        addLogEntry('info', `📄 عدد الصفحات: ${maxPages}`);
        addLogEntry('info', `🔢 البدء من الصفحة: ${startPage}`);
    }

    if (!requestData.query) {
        addLogEntry('error', '❌ يرجى إدخال الاستعلام');
        showAlert('يرجى إدخال الاستعلام', 'danger');
        return false;
    }

    addLogEntry('info', `🔍 الاستعلام: ${requestData.query}`);
    addLogEntry('info', `⏱️ تأخير التحميل: ${requestData.page_load_delay} ثانية`);

    try {
        const response = await fetch('/api/scrape', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (result.success) {
            addLogEntry('success', '✅ تم بدء عملية الاستخراج بنجاح');
            return true;
        } else {
            throw new Error(result.error || 'خطأ غير معروف');
        }

    } catch (error) {
        console.error('خطأ في بدء الاستخراج:', error);
        throw error;
    }
}

// متغير لمراقبة الحالة
let statusMonitoringInterval = null;

function startStatusMonitoring() {
    // إيقاف المراقبة السابقة إن وجدت
    if (statusMonitoringInterval) {
        clearInterval(statusMonitoringInterval);
    }

    // بدء مراقبة الحالة كل ثانية
    statusMonitoringInterval = setInterval(async () => {
        try {
            const response = await fetch('/api/scrape_status');
            const status = await response.json();

            // تحديث حالة الأزرار
            updateScrapingControlButtons(status.is_running);

            // تحديث العداد اللحظي
            if (status.is_running && status.results_count > 0) {
                // حساب النسبة المئوية بناءً على الصفحة الحالية والحد الأقصى
                const currentPage = status.current_page || 1;
                const maxPages = parseInt(document.getElementById('maxPages').value) || 10;
                const pageProgress = Math.min((currentPage / maxPages) * 100, 100);

                const progressStats = {
                    totalCount: status.results_count,
                    moviesCount: status.movies_count || 0,
                    seriesCount: status.series_count || 0,
                    currentPage: currentPage,
                    percentage: Math.round(pageProgress), // نسبة مئوية صحيحة بناءً على الصفحات
                    status: 'جاري الاستخراج...'
                };
                updateProgressStats(progressStats);
            }

            // إذا اكتملت العملية
            if (status.completed) {
                stopStatusMonitoring();

                if (status.error) {
                    addLogEntry('error', `❌ خطأ في العملية: ${status.error}`);
                    showAlert(`خطأ في العملية: ${status.error}`, 'danger');
                } else if (status.data && status.data.length > 0) {
                    // عرض النتائج
                    displayResults(status.data, status.total_items, status.movies_count, status.series_count);
                    addLogEntry('success', `✅ اكتملت العملية بنجاح: ${status.total_items} عنصر`);
                    showAlert(`تم استخراج ${status.total_items} عنصر بنجاح!`, 'success');
                } else {
                    addLogEntry('warning', '⚠️ اكتملت العملية بدون نتائج');
                    showAlert('اكتملت العملية بدون نتائج', 'warning');
                }

                resetScrapingState();
                updateProgressBar(100, 'اكتملت العملية');
                setTimeout(() => {
                    hideProgressStats();
                }, 3000);
            }

            // إذا تم الإيقاف والتصدير
            if (status.status === 'stopped' && status.data) {
                stopStatusMonitoring();
                displayResults(status.data, status.total_items, status.movies_count, status.series_count);
                addLogEntry('success', `✅ تم إيقاف العملية وتصدير ${status.total_items} عنصر`);
                showAlert(`تم إيقاف العملية وتصدير ${status.total_items} عنصر!`, 'success');
                resetScrapingState();
            }

        } catch (error) {
            console.error('خطأ في مراقبة الحالة:', error);
        }
    }, 1000); // فحص كل ثانية
}

function stopStatusMonitoring() {
    if (statusMonitoringInterval) {
        clearInterval(statusMonitoringInterval);
        statusMonitoringInterval = null;
    }
}

// تم حذف الدوال القديمة - يتم استخدام النظام الجديد الآن

// تم حذف دالة startMultiPageScraping - يتم استخدام النظام الجديد الآن

// دالة مساعدة للحصول على معلومات النمط
function getPatternInfo() {
    const patternInfo = document.getElementById('patternInfo');
    if (patternInfo && patternInfo.style.display !== 'none') {
        // البحث عن النمط المحفوظ في البيانات
        const patternData = patternInfo.getAttribute('data-pattern');
        if (patternData) {
            try {
                return JSON.parse(patternData);
            } catch (e) {
                console.error('خطأ في تحليل بيانات النمط:', e);
            }
        }

        // إذا لم توجد البيانات، محاولة استخراجها من النص
        const patternText = patternInfo.textContent;
        const patternMatch = patternText.match(/النمط المكتشف: (.+)/);
        if (patternMatch) {
            return {
                pattern: patternMatch[1],
                variable: '{page}' // افتراضي
            };
        }
    }

    return null;
}

async function debugQuery() {
    const url = document.getElementById('url').value.trim();
    const query = document.getElementById('query').value.trim();
    
    if (!url || !query) {
        showAlert('يرجى إدخال الرابط والاستعلام للتصحيح', 'danger');
        return;
    }
    
    setLoading(true);
    
    try {
        const response = await fetch('/api/debug', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: url,
                query: query
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayDebugInfo(data.debug_info);
        } else {
            showAlert('خطأ في التصحيح: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
    } finally {
        setLoading(false);
    }
}

async function exportResultsOld(exportType = 'multiple', resultsToExport = null) {
    // استخدام النتائج المحددة أو النتائج الحالية
    const dataToExport = resultsToExport || currentResults;

    if (dataToExport.length === 0) {
        addLogEntry('warning', '⚠️ لا توجد نتائج للتصدير');
        showAlert('لا توجد نتائج للتصدير', 'danger');
        return;
    }

    let maxItemsPerFile = parseInt(document.getElementById('maxItemsPerFile').value) || 100;

    // تعديل إعدادات التصدير حسب النوع
    if (exportType === 'single') {
        maxItemsPerFile = dataToExport.length; // ملف واحد يحتوي على جميع البيانات
        addLogEntry('info', '📤 بدء تصدير البيانات في ملف واحد مجمع...');
    } else {
        addLogEntry('info', '📤 بدء تصدير البيانات في ملفات متعددة...');
    }

    addLogEntry('info', `📊 عدد العناصر: ${dataToExport.length}`);
    addLogEntry('info', `📁 عدد العناصر لكل ملف: ${maxItemsPerFile}`);

    setLoading(true);

    try {
        // الحصول على اسم الموقع
        const siteName = getCurrentSiteName();

        const response = await fetch('/api/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                results: dataToExport,
                max_items_per_file: maxItemsPerFile,
                export_type: exportType,
                site_name: siteName
            })
        });

        const data = await response.json();

        if (data.success) {
            addLogEntry('success', `✅ تم تصدير البيانات إلى ${data.files.length} ملف بنجاح!`);

            // عرض تفاصيل الملفات
            data.files.forEach((file, index) => {
                addLogEntry('info', `📄 ملف ${index + 1}: ${file}`);
            });

            showAlert(data.message || `تم تصدير ${data.files.length} ملف بنجاح`, 'success');
            displayExportedFiles(data.files);
        } else {
            addLogEntry('error', `❌ فشل التصدير: ${data.error}`);
            showAlert('خطأ في التصدير: ' + data.error, 'danger');
        }
    } catch (error) {
        addLogEntry('error', `❌ خطأ في الاتصال أثناء التصدير: ${error.message}`);
        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
    } finally {
        setLoading(false);
    }
}

async function analyzePages() {
    const page1 = document.getElementById('page1').value.trim();
    const page2 = document.getElementById('page2').value.trim();
    const page3 = document.getElementById('page3').value.trim();
    
    const pages = [page1, page2, page3].filter(page => page !== '');
    
    if (pages.length < 2) {
        showAlert('يرجى إدخال صفحتين على الأقل لتحليل النمط', 'danger');
        return;
    }
    
    setLoading(true);
    
    try {
        const response = await fetch('/api/analyze_pages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pages: pages
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayPagePattern(data.pattern);
            showAlert('تم تحليل نمط الصفحات بنجاح', 'success');
        } else {
            showAlert('خطأ في تحليل النمط: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في الاتصال: ' + error.message, 'danger');
    } finally {
        setLoading(false);
    }
}

function displayResults(results, totalItems, moviesCount, seriesCount, isTemporaryView = false) {
    const resultsSection = document.getElementById('resultsSection');
    const resultsTable = document.getElementById('resultsTable');

    // حفظ النتائج في المتغيرات العامة (فقط إذا لم تكن عرض مؤقت)
    if (!isTemporaryView) {
        currentResults = results || [];

        // إضافة النتائج الجديدة إلى الإجمالي المستخرج (بدون تكرار)
        if (results && results.length > 0) {
            // إضافة النتائج الجديدة فقط
            const newResults = results.filter(newItem =>
                !totalExtractedResults.some(existingItem =>
                    existingItem.title === newItem.title && existingItem.link === newItem.link
                )
            );
            totalExtractedResults.push(...newResults);
        }

        filteredResults = [...currentResults];
        excludedResults = [];
        includedResults = [];

        // تحديث الإحصائيات العامة
        updateGeneralStats();
    }

    const resultsToDisplay = results || [];

    if (resultsToDisplay.length === 0) {
        resultsSection.innerHTML = '<p>لم يتم العثور على نتائج</p>';
        return;
    }
    
    let tableHTML = `
        <table class="results-table">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>🎬 اسم الفيلم</th>
                    <th>🔗 رابط الفيلم</th>
                    <th>🖼️ صورة الفيلم</th>
                    <th>📂 النوع</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    resultsToDisplay.forEach((item, index) => {
        const movieTitle = item.title || 'اسم غير محدد';
        const movieLink = item.link || '#';
        const movieImage = item.image || '';
        const movieType = item.type === 'movie' ? '🎬 فيلم' :
                         item.type === 'series' ? '📺 مسلسل' :
                         '❓ غير محدد';

        tableHTML += `
            <tr>
                <td><strong>${index + 1}</strong></td>
                <td><strong>${movieTitle}</strong></td>
                <td>
                    ${movieLink !== '#' ?
                        `<a href="${movieLink}" target="_blank" class="btn btn-sm btn-primary">
                            <i class="fas fa-external-link-alt"></i> فتح الفيلم
                        </a>` :
                        '<span class="text-muted">لا يوجد رابط</span>'
                    }
                </td>
                <td>
                    ${movieImage ?
                        `<img src="${movieImage}" alt="${movieTitle}"
                             style="max-width: 80px; max-height: 120px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">` :
                        '<span class="text-muted">لا توجد صورة</span>'
                    }
                </td>
                <td>${movieType}</td>
            </tr>
        `;
    });
    
    tableHTML += '</tbody></table>';
    resultsTable.innerHTML = tableHTML;
    resultsSection.style.display = 'block';

    // إظهار قسم الفلترة وتفعيل الأزرار (فقط إذا لم تكن عرض مؤقت)
    const filterSection = document.querySelector('.results-filter-section');
    if (filterSection && !isTemporaryView && currentResults.length > 0) {
        filterSection.style.display = 'block';

        // تفعيل أزرار الفلترة
        setupFilterButtons();

        // تحديث الإحصائيات العامة
        updateGeneralStats();

        // إضافة رسالة في السجل
        addLogEntry('info', `📊 تم عرض ${currentResults.length} عنصر - الفلاتر متاحة الآن`);
    } else if (!isTemporaryView) {
        // تحديث الإحصائيات فقط
        updateGeneralStats();
    }
}

function setupFilterButtons() {
    // تفعيل أزرار الفلترة بالاسم
    const excludeByNameBtn = document.getElementById('excludeByNameBtn');
    const includeByNameBtn = document.getElementById('includeByNameBtn');

    if (excludeByNameBtn) {
        excludeByNameBtn.onclick = () => filterByName('exclude');
    }

    if (includeByNameBtn) {
        includeByNameBtn.onclick = () => filterByName('include');
    }

    // تفعيل زر فلتر الصور
    const applyImageFilterBtn = document.getElementById('applyImageFilterBtn');
    if (applyImageFilterBtn) {
        applyImageFilterBtn.onclick = applyImageFilter;
    }

    // تفعيل أزرار العرض
    const showAllBtn = document.getElementById('showAllResultsBtn');
    const showExcludedBtn = document.getElementById('showExcludedBtn');
    const showIncludedBtn = document.getElementById('showIncludedBtn');

    if (showAllBtn) showAllBtn.onclick = showAllResults;
    if (showExcludedBtn) showExcludedBtn.onclick = () => showFilteredResults('excluded');
    if (showIncludedBtn) showIncludedBtn.onclick = () => showFilteredResults('included');

    // تفعيل أزرار أخرى
    const resetFiltersBtn = document.getElementById('resetFiltersBtn');

    // أزرار تصدير الإجمالي المستخرج
    const exportTotalCSVBtn = document.getElementById('exportTotalCSVBtn');
    const exportTotalJSONBtn = document.getElementById('exportTotalJSONBtn');

    // أزرار تصدير المفلتر
    const exportFilteredCSVBtn = document.getElementById('exportFilteredCSVBtn');
    const exportFilteredJSONBtn = document.getElementById('exportFilteredJSONBtn');

    if (resetFiltersBtn) resetFiltersBtn.onclick = resetAllFilters;

    // تفعيل أزرار تصدير الإجمالي
    if (exportTotalCSVBtn) exportTotalCSVBtn.onclick = () => exportResults('total', 'csv');
    if (exportTotalJSONBtn) exportTotalJSONBtn.onclick = () => exportResults('total', 'json');

    // تفعيل أزرار تصدير المفلتر
    if (exportFilteredCSVBtn) exportFilteredCSVBtn.onclick = () => exportResults('filtered', 'csv');
    if (exportFilteredJSONBtn) exportFilteredJSONBtn.onclick = () => exportResults('filtered', 'json');

    console.log('✅ تم تفعيل جميع أزرار الفلترة والتصدير الجديدة');
}

function displayDebugInfo(debugInfo) {
    const debuggerSection = document.getElementById('debuggerSection');
    const codePreview = document.getElementById('codePreview');
    
    let debugHTML = `
        <h4>معلومات التصحيح:</h4>
        <p><strong>عدد العناصر المطابقة:</strong> ${debugInfo.matched_elements || 0}</p>
        <p><strong>المحدد المستخدم:</strong> ${debugInfo.selector || 'غير محدد'}</p>
        <div class="code-preview">
            ${debugInfo.html_preview || 'لا يوجد كود للعرض'}
        </div>
    `;
    
    codePreview.innerHTML = debugHTML;
    debuggerSection.style.display = 'block';
}

function displayPagePattern(pattern) {
    const patternInfo = document.getElementById('patternInfo');

    // حفظ بيانات النمط كـ attribute
    patternInfo.setAttribute('data-pattern', JSON.stringify(pattern));

    let patternHTML = `
        <h4>نمط الصفحات المكتشف:</h4>
        <p><strong>النمط:</strong> ${pattern.pattern || 'لم يتم اكتشاف نمط'}</p>
        <p><strong>المتغير:</strong> ${pattern.variable || 'غير محدد'}</p>
        <p><strong>الخطوة:</strong> ${pattern.step || 'غير محدد'}</p>
    `;

    if (pattern.next_pages && pattern.next_pages.length > 0) {
        patternHTML += '<h5>الصفحات التالية المتوقعة:</h5><ul>';
        pattern.next_pages.forEach(page => {
            patternHTML += `<li><a href="${page}" target="_blank">${page}</a></li>`;
        });
        patternHTML += '</ul>';
    }

    patternInfo.innerHTML = patternHTML;
    patternInfo.style.display = 'block';
}

function displayExportedFiles(files) {
    const exportInfo = document.getElementById('exportInfo');
    
    let filesHTML = '<h4>الملفات المصدرة:</h4><ul>';
    files.forEach(file => {
        filesHTML += `<li><a href="/exports/${file}" download>${file}</a></li>`;
    });
    filesHTML += '</ul>';
    
    exportInfo.innerHTML = filesHTML;
    exportInfo.style.display = 'block';
}

function updateQueryPreview() {
    const query = document.getElementById('query').value;
    const preview = document.getElementById('queryPreview');

    if (query.trim()) {
        // تحليل الاستعلام لإظهار ما يبحث عنه
        let previewText = `معاينة الاستعلام: ${query}`;

        // إضافة توضيح للاستعلام الافتراضي
        if (query.toLowerCase().includes('title') && query.toLowerCase().includes('link') && query.toLowerCase().includes('image')) {
            previewText += '\n🎬 سيتم البحث عن: اسم الفيلم، رابط الفيلم، ورابط صورة الفيلم';
        }

        preview.textContent = previewText;
        preview.style.display = 'block';
        preview.style.whiteSpace = 'pre-line'; // للسماح بالأسطر الجديدة
    } else {
        preview.style.display = 'none';
    }
}

function validateUrl() {
    const url = document.getElementById('url').value;
    const urlInput = document.getElementById('url');
    
    try {
        new URL(url);
        urlInput.style.borderColor = '#27ae60';
    } catch {
        if (url.trim()) {
            urlInput.style.borderColor = '#e74c3c';
        } else {
            urlInput.style.borderColor = '#e9ecef';
        }
    }
}

function setLoading(loading) {
    isLoading = loading;
    const buttons = document.querySelectorAll('.btn');
    const loadingDiv = document.getElementById('loading');

    // أزرار التحكم التي يجب أن تبقى نشطة
    const controlButtons = [
        'pauseScrapingBtn',
        'resumeScrapingBtn',
        'stopAndExportBtn',
        'scrollToBottomBtn',
        'clearLogBtn'
    ];

    buttons.forEach(btn => {
        // تحقق من أن الزر ليس من أزرار التحكم
        if (!controlButtons.includes(btn.id)) {
            btn.disabled = loading;
            if (loading) {
                btn.style.opacity = '0.6';
            } else {
                btn.style.opacity = '1';
            }
        }
    });

    // التأكد من أن أزرار التحكم تبقى نشطة
    controlButtons.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
            btn.disabled = false;
            btn.style.opacity = '1';
            btn.style.pointerEvents = 'auto';
        }
    });

    if (loading) {
        loadingDiv.style.display = 'block';
        // إضافة class للتحكم في CSS
        document.body.classList.add('loading');
    } else {
        loadingDiv.style.display = 'none';
        document.body.classList.remove('loading');
    }
}

function showAlert(message, type) {
    const alertsContainer = document.getElementById('alerts');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;

    alertsContainer.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// ===== وظائف سجل التطور اللحظي =====

function addLogEntry(type, message) {
    const log = document.getElementById('progressLog');
    const timestamp = new Date().toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;
    entry.innerHTML = `
        <span class="timestamp">[${timestamp}]</span>
        <span class="message">${message}</span>
    `;

    log.appendChild(entry);

    // التمرير التلقائي للأسفل
    log.scrollTop = log.scrollHeight;

    // الحد الأقصى لعدد الإدخالات (100 إدخال)
    const entries = log.querySelectorAll('.log-entry');
    if (entries.length > 100) {
        entries[0].remove();
    }
}

function clearLog() {
    const log = document.getElementById('progressLog');
    log.innerHTML = `
        <div class="log-entry info">
            <span class="timestamp">[جاهز]</span>
            <span class="message">🚀 تم مسح السجل - التطبيق جاهز للاستخدام</span>
        </div>
    `;
}

function scrollLogToBottom() {
    const log = document.getElementById('progressLog');
    log.scrollTop = log.scrollHeight;
}

// ===== وظائف جديدة =====

function toggleScrapingMode() {
    const singlePageMode = document.getElementById('singlePageMode').checked;
    const multiPageMode = document.getElementById('multiPageMode').checked;
    const singlePageSection = document.getElementById('singlePageSection');
    const multiPageSection = document.getElementById('multiPageSection');

    if (singlePageMode) {
        singlePageSection.style.display = 'block';
        multiPageSection.style.display = 'none';
        addLogEntry('info', '🔄 تم التبديل إلى وضع الصفحة المنفردة');
    } else if (multiPageMode) {
        singlePageSection.style.display = 'none';
        multiPageSection.style.display = 'block';
        addLogEntry('info', '🔄 تم التبديل إلى وضع الصفحات المتتالية (الوضع الافتراضي)');
    }
}

async function scrapeMultiplePages(pattern, query, maxPages, startFromPage, maxItemsPerFile) {
    addLogEntry('info', `🔄 بدء الاستخراج المتتالي من ${maxPages} صفحة...`);

    // حفظ بيانات العملية للاستئناف
    currentScrapingData = {
        pattern,
        query,
        maxPages,
        startFromPage,
        maxItemsPerFile,
        currentPage: startFromPage
    };

    // تحديث إحصائيات التقدم
    scrapingStats.totalPages = maxPages;
    scrapingStats.currentPage = 0;
    updateProgressStats(scrapingStats);

    try {
        // استخراج متتالي مع إمكانية الإيقاف
        await scrapeMultiplePagesWithProgress(pattern, query, maxPages, startFromPage, maxItemsPerFile);

    } catch (error) {
        if (!scrapingAborted) {
            addLogEntry('error', `❌ خطأ في الاستخراج المتتالي: ${error.message}`);
            showAlert('خطأ في الاستخراج المتتالي: ' + error.message, 'danger');
        }
    }
}

async function scrapeMultiplePagesWithProgress(pattern, query, maxPages, startFromPage, maxItemsPerFile) {
    currentResults = [];
    let totalScraped = 0;

    for (let i = 0; i < maxPages; i++) {
        // التحقق من الإيقاف
        if (scrapingAborted) {
            addLogEntry('warning', '⏸️ تم إيقاف العملية');
            break;
        }

        const pageNumber = startFromPage + i;
        const pageUrl = pattern.pattern.replace('{num}', pageNumber);

        // تحديث التقدم
        scrapingStats.currentPage = i + 1;
        scrapingStats.percentage = ((i + 1) / maxPages) * 100;
        updateProgressStats(scrapingStats);

        addLogEntry('info', `📄 معالجة الصفحة ${i + 1}/${maxPages}: ${pageUrl}`);

        try {
            const pageLoadDelay = parseFloat(document.getElementById('pageLoadDelay').value) || 2;

            const response = await fetch('/api/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: pageUrl,
                    query: query,
                    max_pages: 1,
                    max_items_per_file: maxItemsPerFile,
                    scraping_mode: 'single',
                    page_load_delay: pageLoadDelay
                })
            });

            const data = await response.json();

            if (data.success && data.data.length > 0) {
                currentResults = currentResults.concat(data.data);
                totalScraped += data.data.length;

                // تحديث الإحصائيات
                const movies = data.data.filter(item => item.type === 'movie').length;
                const series = data.data.filter(item => item.type === 'series').length;

                scrapingStats.moviesCount += movies;
                scrapingStats.seriesCount += series;
                scrapingStats.totalCount = totalScraped;
                updateProgressStats(scrapingStats);

                addLogEntry('success', `✅ الصفحة ${i + 1}: تم استخراج ${data.data.length} عنصر`);
                addLogEntry('info', `📊 إجمالي العناصر حتى الآن: ${totalScraped}`);

                // تحديث النتائج في الوقت الفعلي
                displayResults(currentResults);
            } else {
                addLogEntry('warning', `⚠️ الصفحة ${i + 1}: لم يتم العثور على بيانات`);
            }

            // توقف قصير بين الصفحات لتجنب الحظر
            if (i < maxPages - 1 && !scrapingAborted) {
                addLogEntry('info', '⏳ انتظار قصير قبل الصفحة التالية...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

        } catch (error) {
            addLogEntry('error', `❌ خطأ في الصفحة ${i + 1}: ${error.message}`);
        }
    }

    // النتائج النهائية
    if (!scrapingAborted) {
        const movies = currentResults.filter(item => item.type === 'movie').length;
        const series = currentResults.filter(item => item.type === 'series').length;

        // تحديث النسبة المئوية إلى 100% عند الانتهاء
        scrapingStats.percentage = 100;
        scrapingStats.status = 'اكتمل الاستخراج';
        updateProgressStats(scrapingStats);

        addLogEntry('success', `🎉 انتهى الاستخراج المتتالي! إجمالي العناصر: ${totalScraped}`);
        if (movies > 0) addLogEntry('info', `🎬 أفلام: ${movies}`);
        if (series > 0) addLogEntry('info', `📺 مسلسلات: ${series}`);

        showAlert(`تم استخراج ${totalScraped} عنصر من ${scrapingStats.currentPage} صفحة`, 'success');
    }
}

async function continueMultiPageScraping(scrapingData) {
    // استئناف العملية من الصفحة الحالية
    const remainingPages = scrapingData.maxPages - scrapingStats.currentPage;
    const newStartPage = scrapingData.startFromPage + scrapingStats.currentPage;

    addLogEntry('info', `🔄 استئناف الاستخراج من الصفحة ${newStartPage}...`);

    await scrapeMultiplePagesWithProgress(
        scrapingData.pattern,
        scrapingData.query,
        remainingPages,
        newStartPage,
        scrapingData.maxItemsPerFile
    );
}

function showExportSettings() {
    const modal = new bootstrap.Modal(document.getElementById('exportSettingsModal'));
    modal.show();
}

async function selectExportFolder() {
    try {
        // استخدام File System Access API إذا كان متاحاً
        if ('showDirectoryPicker' in window) {
            const directoryHandle = await window.showDirectoryPicker();
            document.getElementById('exportPath').value = directoryHandle.name;

            // حفظ handle للاستخدام لاحقاً
            window.selectedDirectoryHandle = directoryHandle;

            addLogEntry('info', `📂 تم اختيار مجلد التصدير: ${directoryHandle.name}`);
            showAlert('تم اختيار مجلد التصدير بنجاح', 'success');
        } else {
            // fallback للمتصفحات التي لا تدعم API
            showAlert('متصفحك لا يدعم اختيار المجلدات. سيتم التحميل في مجلد التحميلات الافتراضي.', 'info');
        }
    } catch (error) {
        if (error.name !== 'AbortError') {
            addLogEntry('error', `❌ خطأ في اختيار المجلد: ${error.message}`);
            showAlert('خطأ في اختيار المجلد', 'danger');
        }
    }
}

function applyExportSettings() {
    const exportType = document.getElementById('exportType').value;
    const itemsPerFile = parseInt(document.getElementById('exportItemsPerFile').value) || 100;
    const format = document.getElementById('exportFormat').value;

    // تحديث القيم في النموذج الرئيسي
    document.getElementById('maxItemsPerFile').value = itemsPerFile;

    addLogEntry('info', `⚙️ تطبيق إعدادات التصدير: ${exportType}, ${itemsPerFile} عنصر/ملف, تنسيق: ${format}`);

    // إغلاق النافذة المنبثقة
    const modal = bootstrap.Modal.getInstance(document.getElementById('exportSettingsModal'));
    modal.hide();

    // بدء التصدير
    exportResults(exportType);
}

// دالة مساعدة لتحميل الملفات
function downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// وظائف التحكم في العمليات الجديدة
function pauseScraping() {
    if (!isScrapingActive) return;

    // تعيين الحالة فوراً لاستجابة سريعة
    scrapingPaused = true;
    addLogEntry('warning', '⏸️ جاري إيقاف العملية مؤقتاً...');

    // تحديث أزرار التحكم فوراً
    document.getElementById('pauseScrapingBtn').style.display = 'none';
    document.getElementById('resumeScrapingBtn').style.display = 'inline-block';

    // إرسال الطلب للخادم
    fetch('/api/control_scraping', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'pause'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('warning', `⏸️ ${data.message}`);
            showAlert(data.message, 'warning');
        } else {
            // إعادة تعيين الحالة في حالة الفشل
            scrapingPaused = false;
            document.getElementById('pauseScrapingBtn').style.display = 'inline-block';
            document.getElementById('resumeScrapingBtn').style.display = 'none';

            addLogEntry('error', `❌ خطأ في إيقاف العملية: ${data.error}`);
            showAlert('خطأ في إيقاف العملية', 'danger');
        }
    })
    .catch(error => {
        // إعادة تعيين الحالة في حالة الفشل
        scrapingPaused = false;
        document.getElementById('pauseScrapingBtn').style.display = 'inline-block';
        document.getElementById('resumeScrapingBtn').style.display = 'none';

        addLogEntry('error', `❌ خطأ في الاتصال: ${error.message}`);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

function resumeScraping() {
    if (!isScrapingActive) return;

    // تعيين الحالة فوراً لاستجابة سريعة
    scrapingPaused = false;
    addLogEntry('success', '▶️ جاري استئناف العملية...');

    // تحديث أزرار التحكم فوراً
    document.getElementById('pauseScrapingBtn').style.display = 'inline-block';
    document.getElementById('resumeScrapingBtn').style.display = 'none';

    // إرسال الطلب للخادم
    fetch('/api/control_scraping', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'resume'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('success', `▶️ ${data.message}`);
            showAlert(data.message, 'success');
        } else {
            // إعادة تعيين الحالة في حالة الفشل
            scrapingPaused = true;
            document.getElementById('pauseScrapingBtn').style.display = 'none';
            document.getElementById('resumeScrapingBtn').style.display = 'inline-block';

            addLogEntry('error', `❌ خطأ في استئناف العملية: ${data.error}`);
            showAlert('خطأ في استئناف العملية', 'danger');
        }
    })
    .catch(error => {
        // إعادة تعيين الحالة في حالة الفشل
        scrapingPaused = true;
        document.getElementById('pauseScrapingBtn').style.display = 'none';
        document.getElementById('resumeScrapingBtn').style.display = 'inline-block';

        addLogEntry('error', `❌ خطأ في الاتصال: ${error.message}`);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

function stopAndExportScraping() {
    if (!isScrapingActive) return;

    if (!confirm('هل تريد إيقاف العملية وتصدير النتائج الحالية؟')) {
        return;
    }

    // تعيين الحالة فوراً لاستجابة سريعة
    scrapingAborted = true;
    scrapingPaused = false;
    addLogEntry('warning', '🛑 جاري إيقاف العملية وتصدير النتائج...');

    // إخفاء أزرار التحكم فوراً
    document.getElementById('pauseScrapingBtn').style.display = 'none';
    document.getElementById('resumeScrapingBtn').style.display = 'none';
    document.getElementById('stopAndExportBtn').style.display = 'none';

    // إرسال الطلب للخادم
    fetch('/api/control_scraping', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'stop_and_export'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            isScrapingActive = false;

            addLogEntry('warning', `🛑 ${data.message}`);

            // إظهار معلومات التصدير إذا كانت متوفرة
            if (data.exported_files && data.exported_files.length > 0) {
                displayExportInfo(data.exported_files, data.total_items);
            }

            showAlert(data.message, 'warning');

            // إعادة تعيين حالة التطبيق
            resetScrapingState();
        } else {
            addLogEntry('error', `❌ خطأ في إيقاف العملية: ${data.error}`);
            showAlert('خطأ في إيقاف العملية', 'danger');
        }
    })
    .catch(error => {
        addLogEntry('error', `❌ خطأ في الاتصال: ${error.message}`);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// وظائف تصفية النتائج
function filterResults(filterType) {
    const searchTerm = document.getElementById('filterSearchTerm').value.trim();

    if (!searchTerm) {
        showAlert('يرجى إدخال كلمة البحث', 'warning');
        return;
    }

    if (currentResults.length === 0) {
        showAlert('لا توجد نتائج للتصفية', 'warning');
        return;
    }

    addLogEntry('info', `🔍 بدء تصفية النتائج بالكلمة: "${searchTerm}"`);

    fetch('/api/filter_results', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            results: currentResults,
            filter_type: filterType,
            search_term: searchTerm
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // حفظ النتائج المصفاة
            filteredResults = data.filtered_results;
            excludedResults = data.total_excluded > 0 ? currentResults.filter(item =>
                !data.filtered_results.some(filtered => filtered.title === item.title)
            ) : [];
            includedResults = data.total_included > 0 ? currentResults.filter(item =>
                item.title.toLowerCase().includes(searchTerm.toLowerCase())
            ) : [];

            currentFilterTerm = searchTerm;

            // عرض النتائج المصفاة
            displayResults(filteredResults);

            // عرض معلومات التصفية
            showFilterInfo(data.message, data.total_filtered, data.total_excluded, data.total_included);

            addLogEntry('success', `✅ ${data.message}`);
            showAlert(data.message, 'success');
        } else {
            addLogEntry('error', `❌ خطأ في التصفية: ${data.error}`);
            showAlert('خطأ في التصفية', 'danger');
        }
    })
    .catch(error => {
        addLogEntry('error', `❌ خطأ في الاتصال: ${error.message}`);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

function showAllResults() {
    if (currentResults.length === 0) {
        showAlert('لا توجد نتائج لعرضها', 'warning');
        return;
    }

    // إعادة تعيين الفلاتر وعرض جميع النتائج الأصلية
    filteredResults = [...currentResults];
    excludedResults = [];
    includedResults = [];

    displayResults(currentResults);
    updateGeneralStats();
    addLogEntry('info', `📋 عرض جميع النتائج الأصلية قبل الفلترة (${currentResults.length} عنصر)`);
    showAlert(`تم عرض جميع النتائج الأصلية (${currentResults.length} عنصر)`, 'success');
}

function showFilteredResults(type) {
    let resultsToShow = [];
    let message = '';

    if (type === 'excluded') {
        if (excludedResults.length === 0) {
            showAlert('لا توجد عناصر مستبعدة', 'warning');
            return;
        }
        resultsToShow = excludedResults;
        message = `عرض العناصر المستبعدة (${excludedResults.length} عنصر)`;
    } else if (type === 'included') {
        if (includedResults.length === 0) {
            showAlert('لا توجد عناصر مختارة', 'warning');
            return;
        }
        resultsToShow = includedResults;
        message = `عرض العناصر المختارة (${includedResults.length} عنصر)`;
    } else {
        showAlert('نوع العرض غير صحيح', 'warning');
        return;
    }

    displayResults(resultsToShow);
    addLogEntry('info', `👁️ ${message}`);
    showAlert(message, 'info');
}





function resetScrapingState() {
    // إيقاف مراقبة التحكم
    stopControlMonitoring();

    // إعادة تعيين حالة الأزرار
    document.getElementById('scrapeBtn').disabled = false;
    document.getElementById('scrapeBtn').innerHTML = '<i class="fas fa-play"></i> بدء الاستخراج';

    // إخفاء أزرار التحكم
    document.getElementById('pauseScrapingBtn').style.display = 'none';
    document.getElementById('resumeScrapingBtn').style.display = 'none';
    document.getElementById('stopAndExportBtn').style.display = 'none';

    // إخفاء إحصائيات التقدم
    hideProgressStats();

    // إعادة تعيين المتغيرات
    isScrapingActive = false;
    scrapingPaused = false;
    scrapingAborted = false;
}

// تحديث وظيفة بدء الاستخراج لإظهار أزرار التحكم
function updateScrapingControlButtons(show = false) {
    const pauseBtn = document.getElementById('pauseScrapingBtn');
    const resumeBtn = document.getElementById('resumeScrapingBtn');
    const stopBtn = document.getElementById('stopAndExportBtn');

    if (show && isScrapingActive) {
        pauseBtn.style.display = 'inline-block';
        resumeBtn.style.display = scrapingPaused ? 'inline-block' : 'none';
        stopBtn.style.display = 'inline-block';
    } else {
        pauseBtn.style.display = 'none';
        resumeBtn.style.display = 'none';
        stopBtn.style.display = 'none';
    }
}

// وظائف مراقبة حالة التحكم
function startControlMonitoring() {
    // إيقاف أي مراقبة سابقة
    if (controlCheckInterval) {
        clearInterval(controlCheckInterval);
    }

    // بدء مراقبة دورية كل 500 مللي ثانية
    controlCheckInterval = setInterval(() => {
        // التأكد من أن الأزرار تعمل
        enableControlButtons();

        // فحص حالة الإيقاف المؤقت
        if (scrapingPaused) {
            // إظهار رسالة الإيقاف المؤقت
            updateProgressBar(null, 'متوقف مؤقتاً - اضغط استئناف للمتابعة');
        }

        // فحص حالة الإيقاف النهائي
        if (scrapingAborted) {
            stopControlMonitoring();
        }
    }, 500);
}

function stopControlMonitoring() {
    if (controlCheckInterval) {
        clearInterval(controlCheckInterval);
        controlCheckInterval = null;
    }
}

function enableControlButtons() {
    // التأكد من أن الأزرار قابلة للنقر
    const pauseBtn = document.getElementById('pauseScrapingBtn');
    const resumeBtn = document.getElementById('resumeScrapingBtn');
    const stopBtn = document.getElementById('stopAndExportBtn');
    const scrollBtn = document.getElementById('scrollToBottomBtn');
    const clearBtn = document.getElementById('clearLogBtn');

    if (pauseBtn) {
        pauseBtn.disabled = false;
        pauseBtn.style.pointerEvents = 'auto';
    }
    if (resumeBtn) {
        resumeBtn.disabled = false;
        resumeBtn.style.pointerEvents = 'auto';
    }
    if (stopBtn) {
        stopBtn.disabled = false;
        stopBtn.style.pointerEvents = 'auto';
    }
    if (scrollBtn) {
        scrollBtn.disabled = false;
        scrollBtn.style.pointerEvents = 'auto';
    }
    if (clearBtn) {
        clearBtn.disabled = false;
        clearBtn.style.pointerEvents = 'auto';
    }
}

// وظيفة محسنة لانتظار مع فحص حالة التحكم
async function waitWithControlCheck(ms) {
    const startTime = Date.now();

    while (Date.now() - startTime < ms) {
        // فحص حالة الإيقاف
        if (scrapingAborted) {
            throw new Error('تم إيقاف العملية بواسطة المستخدم');
        }

        // فحص حالة الإيقاف المؤقت
        while (scrapingPaused && !scrapingAborted) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // انتظار قصير قبل الفحص التالي
        await new Promise(resolve => setTimeout(resolve, 100));
    }
}

// وظائف التصفية المحدثة

// تصفية بالاسم مع دعم كلمات متعددة
function filterByName(filterType) {
    const searchTerms = document.getElementById('filterSearchTerm').value.trim();

    // جمع الكلمات الافتراضية المحددة
    const defaultKeywords = [];
    const checkboxes = document.querySelectorAll('.default-keyword-checkbox:checked');
    checkboxes.forEach(checkbox => {
        defaultKeywords.push(checkbox.value.toLowerCase());
    });

    // دمج الكلمات الافتراضية مع الكلمات الإضافية
    let allKeywords = [...defaultKeywords];

    if (searchTerms) {
        const additionalKeywords = searchTerms.split(',').map(term => term.trim().toLowerCase()).filter(term => term);
        allKeywords = allKeywords.concat(additionalKeywords);
    }

    if (allKeywords.length === 0) {
        showAlert('يرجى اختيار كلمات افتراضية أو إدخال كلمات للبحث', 'warning');
        return;
    }

    if (!currentResults || currentResults.length === 0) {
        showAlert('لا توجد نتائج للتصفية', 'warning');
        return;
    }

    // إزالة الكلمات المكررة
    const keywords = [...new Set(allKeywords)];

    currentFilterTerm = searchTerms;

    if (filterType === 'exclude') {
        // استبعاد الأفلام المحتوية على أي من الكلمات
        excludedResults = currentResults.filter(item => {
            if (!item.title) return false;
            const titleLower = item.title.toLowerCase();
            return keywords.some(keyword => titleLower.includes(keyword));
        });

        filteredResults = currentResults.filter(item => {
            if (!item.title) return true;
            const titleLower = item.title.toLowerCase();
            return !keywords.some(keyword => titleLower.includes(keyword));
        });

        includedResults = [];

        addLogEntry('info', `🔍 تم استبعاد ${excludedResults.length} عنصر يحتوي على الكلمات: ${keywords.join(', ')}`);
        showAlert(`تم استبعاد ${excludedResults.length} عنصر`, 'success');

    } else if (filterType === 'include') {
        // اختيار الأفلام المحتوية على أي من الكلمات
        includedResults = currentResults.filter(item => {
            if (!item.title) return false;
            const titleLower = item.title.toLowerCase();
            return keywords.some(keyword => titleLower.includes(keyword));
        });

        filteredResults = includedResults;
        excludedResults = currentResults.filter(item => {
            if (!item.title) return true;
            const titleLower = item.title.toLowerCase();
            return !keywords.some(keyword => titleLower.includes(keyword));
        });

        addLogEntry('info', `🔍 تم اختيار ${includedResults.length} عنصر يحتوي على الكلمات: ${keywords.join(', ')}`);
        showAlert(`تم اختيار ${includedResults.length} عنصر`, 'success');
    }

    // تحديث الإحصائيات
    updateGeneralStats();

    // عرض النتائج المصفاة
    displayResults(filteredResults);
}

// تصفية بالصور
function applyImageFilter() {
    if (!currentResults || currentResults.length === 0) {
        showAlert('لا توجد نتائج للتصفية', 'warning');
        return;
    }

    const excludeNoImage = document.getElementById('excludeNoImageItems').checked;

    if (!excludeNoImage) {
        showAlert('يرجى تحديد خيار التصفية بالصور', 'warning');
        return;
    }

    // كلمات تدل على عدم وجود صورة
    const noImageIndicators = [
        'لا توجد صورة',
        'لا توجد لها صورة',
        'no image',
        'no picture',
        'صورة غير متوفرة',
        'غير متوفرة',
        'N/A',
        'n/a',
        'null',
        'undefined',
        ''
    ];

    if (excludeNoImage) {
        // تحديد العناصر التي لا توجد لها صورة صالحة
        const itemsWithoutValidImage = currentResults.filter(item => {
            // إذا لم توجد صورة أصلاً
            if (!item.image) return true;

            const imageLower = item.image.toLowerCase().trim();

            // إذا كانت الصورة فارغة أو تحتوي على مؤشرات عدم وجود صورة
            if (imageLower === '' || imageLower === 'null' || imageLower === 'undefined') {
                return true;
            }

            // إذا كانت تحتوي على كلمات تدل على عدم وجود صورة
            return noImageIndicators.some(indicator => {
                if (indicator === '') return imageLower === '';
                return imageLower === indicator.toLowerCase() ||
                       imageLower.includes(indicator.toLowerCase());
            });
        });

        // تحديد العناصر التي لها صور صالحة
        const itemsWithValidImage = currentResults.filter(item => {
            // إذا لم توجد صورة أصلاً
            if (!item.image) return false;

            const imageLower = item.image.toLowerCase().trim();

            // إذا كانت الصورة فارغة أو تحتوي على مؤشرات عدم وجود صورة
            if (imageLower === '' || imageLower === 'null' || imageLower === 'undefined') {
                return false;
            }

            // إذا كانت تحتوي على كلمات تدل على عدم وجود صورة
            const hasNoImageIndicator = noImageIndicators.some(indicator => {
                if (indicator === '') return imageLower === '';
                return imageLower === indicator.toLowerCase() ||
                       imageLower.includes(indicator.toLowerCase());
            });

            // إذا كانت تبدأ بـ http أو https فهي صورة صالحة (على الأرجح)
            const isValidUrl = imageLower.startsWith('http://') || imageLower.startsWith('https://');

            return !hasNoImageIndicator && (isValidUrl || imageLower.length > 10);
        });

        excludedResults = itemsWithoutValidImage;
        filteredResults = itemsWithValidImage;
        includedResults = itemsWithValidImage;

        addLogEntry('info', `🖼️ تم استبعاد ${excludedResults.length} عنصر بدون صورة صالحة، متبقي ${filteredResults.length} عنصر`);

        if (filteredResults.length === 0) {
            showAlert(`تم استبعاد جميع العناصر (${excludedResults.length}) لعدم وجود صور صالحة`, 'warning');
        } else {
            showAlert(`تم استبعاد ${excludedResults.length} عنصر بدون صورة، متبقي ${filteredResults.length} عنصر`, 'success');
        }
    }

    // تحديث الإحصائيات
    updateGeneralStats();

    // عرض النتائج المصفاة
    displayResults(filteredResults);
}

// إعادة تعيين جميع الفلاتر
function resetAllFilters() {
    // إعادة تعيين المتغيرات
    filteredResults = [...currentResults];
    excludedResults = [];
    includedResults = [];
    currentFilterTerm = '';

    // إعادة تعيين الحقول
    document.getElementById('filterSearchTerm').value = '';
    document.getElementById('excludeNoImageItems').checked = false;

    // إعادة تعيين الكلمات الافتراضية
    const defaultKeywordCheckboxes = document.querySelectorAll('.default-keyword-checkbox');
    defaultKeywordCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        // إعادة تعيين التأثيرات البصرية
        const label = checkbox.nextElementSibling;
        const badge = label.querySelector('.badge');
        if (badge) {
            badge.style.transform = 'scale(1)';
            badge.style.boxShadow = 'none';
        }
    });

    // تحديث الإحصائيات
    updateGeneralStats();

    // عرض جميع النتائج
    displayResults(currentResults);

    addLogEntry('info', '🔄 تم إعادة تعيين جميع الفلاتر');
    showAlert('تم إعادة تعيين جميع الفلاتر', 'info');
}

// تحديث إحصائيات التصفية
function updateGeneralStats() {
    const generalStatsInfo = document.getElementById('generalStatsInfo');
    const totalExtractedCount = document.getElementById('totalExtractedCount');
    const currentlyDisplayedCount = document.getElementById('currentlyDisplayedCount');
    const filterDetailsSection = document.getElementById('filterDetailsSection');
    const excludedByFilterCount = document.getElementById('excludedByFilterCount');
    const remainingAfterFilterCount = document.getElementById('remainingAfterFilterCount');
    const filterInfoText = document.getElementById('filterInfoText');

    if (!generalStatsInfo) return;

    const totalExtracted = totalExtractedResults.length;
    const currentDisplayed = filteredResults.length;
    const excludedByFilter = excludedResults.length;
    const remainingAfterFilter = currentResults.length - excludedByFilter;

    // تحديث العدادات الرئيسية
    if (totalExtractedCount) totalExtractedCount.textContent = totalExtracted;
    if (currentlyDisplayedCount) currentlyDisplayedCount.textContent = currentDisplayed;

    // إظهار تفاصيل الفلترة إذا تم تطبيق فلاتر
    const hasFilters = excludedByFilter > 0 || currentFilterTerm;

    if (hasFilters && filterDetailsSection) {
        filterDetailsSection.style.display = 'block';

        if (excludedByFilterCount) excludedByFilterCount.textContent = excludedByFilter;
        if (remainingAfterFilterCount) remainingAfterFilterCount.textContent = remainingAfterFilter;

        // تحديث النص التوضيحي
        let infoText = '';
        if (currentFilterTerm) {
            infoText += `فلتر البحث: "${currentFilterTerm}"`;
        }

        if (!infoText) {
            infoText = 'تم تطبيق فلاتر';
        }

        if (filterInfoText) filterInfoText.textContent = infoText;
    } else if (filterDetailsSection) {
        filterDetailsSection.style.display = 'none';
    }

    // إظهار القسم
    generalStatsInfo.style.display = 'block';

    // تحديث حالة أزرار التصدير
    updateExportButtonsState(totalExtracted, currentDisplayed);

    // إضافة رسالة في السجل
    if (hasFilters) {
        addLogEntry('info', `📊 الإحصائيات: إجمالي مستخرج ${totalExtracted} | معروض ${currentDisplayed} | مستبعد بالفلتر ${excludedByFilter} | متبقي ${remainingAfterFilter}`);
    } else {
        addLogEntry('info', `📊 الإحصائيات: إجمالي مستخرج ${totalExtracted} | معروض ${currentDisplayed}`);
    }
}

function updateExportButtonsState(totalExtracted, currentDisplayed) {
    // أزرار تصدير الإجمالي
    const exportTotalCSVBtn = document.getElementById('exportTotalCSVBtn');
    const exportTotalJSONBtn = document.getElementById('exportTotalJSONBtn');

    if (exportTotalCSVBtn) exportTotalCSVBtn.disabled = totalExtracted === 0;
    if (exportTotalJSONBtn) exportTotalJSONBtn.disabled = totalExtracted === 0;

    // أزرار تصدير المفلتر
    const exportFilteredCSVBtn = document.getElementById('exportFilteredCSVBtn');
    const exportFilteredJSONBtn = document.getElementById('exportFilteredJSONBtn');

    if (exportFilteredCSVBtn) exportFilteredCSVBtn.disabled = currentDisplayed === 0;
    if (exportFilteredJSONBtn) exportFilteredJSONBtn.disabled = currentDisplayed === 0;

    // أزرار العرض (إذا كانت موجودة)
    const showExcludedBtn = document.getElementById('showExcludedBtn');
    const showIncludedBtn = document.getElementById('showIncludedBtn');

    if (showExcludedBtn) {
        showExcludedBtn.innerHTML = `<i class="fas fa-eye-slash"></i> عرض المستبعدة`;
        showExcludedBtn.disabled = excludedResults.length === 0;
    }

    if (showIncludedBtn) {
        showIncludedBtn.innerHTML = `<i class="fas fa-eye"></i> عرض المختارة`;
        showIncludedBtn.disabled = (currentResults.length - excludedResults.length) === 0;
    }
}

// متغير لمنع التصدير المتكرر
let isExporting = false;

// دالة التصدير المحسنة مع دعم CSV و JSON
function exportResults(type, format = 'csv') {
    // منع التصدير المتكرر
    if (isExporting) {
        console.log('تصدير قيد التنفيذ بالفعل، تم تجاهل الطلب');
        return;
    }

    isExporting = true;

    try {
        let dataToExport = [];
        let filename = '';

        switch(type) {
            case 'total':
                dataToExport = totalExtractedResults;
                filename = 'إجمالي_المستخرج';
                if (dataToExport.length === 0) {
                    showAlert('لا توجد نتائج مستخرجة للتصدير', 'warning');
                    return;
                }
                break;
            case 'filtered':
                dataToExport = filteredResults;
                filename = 'النتائج_المفلترة';
                if (dataToExport.length === 0) {
                    showAlert('لا توجد نتائج مفلترة للتصدير', 'warning');
                    return;
                }
                break;
            case 'excluded':
                dataToExport = excludedResults;
                filename = 'النتائج_المستبعدة';
                if (dataToExport.length === 0) {
                    showAlert('لا توجد نتائج مستبعدة للتصدير', 'warning');
                    return;
                }
                break;
            case 'included':
                // المختارة = الحالية - المستبعدة
                dataToExport = currentResults.filter(item => !excludedResults.includes(item));
                filename = 'النتائج_المختارة';
                if (dataToExport.length === 0) {
                    showAlert('لا توجد نتائج مختارة للتصدير', 'warning');
                    return;
                }
                break;
            default:
                dataToExport = filteredResults;
                filename = 'النتائج_الافتراضية';
        }

        if (dataToExport.length === 0) {
            showAlert('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        console.log(`بدء تصدير ${type} بصيغة ${format}:`, dataToExport.length, 'عنصر');

        if (format === 'json') {
            exportAsJSON(dataToExport, filename, type);
        } else {
            exportAsCSV(dataToExport, filename, type);
        }
    } finally {
        // إعادة تعيين العلامة بعد ثانية واحدة لضمان اكتمال التصدير
        setTimeout(() => {
            isExporting = false;
        }, 1000);
    }
}

function exportAsCSV(dataToExport, filename, type) {
    // إنشاء البيانات للتصدير CSV
    const exportData = dataToExport.map((item, index) => ({
        'الرقم': index + 1,
        'اسم الفيلم': item.title || 'غير محدد',
        'رابط الفيلم': item.link || 'غير متوفر',
        'صورة الفيلم': item.image || 'غير متوفرة',
        'النوع': item.type === 'movie' ? 'فيلم' :
                item.type === 'series' ? 'مسلسل' : 'غير محدد'
    }));

    // تحويل إلى CSV
    const csvContent = convertToCSV(exportData);

    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // تنظيف الرابط
    URL.revokeObjectURL(url);

    // رسالة نجاح
    const typeText = getTypeText(type);
    addLogEntry('success', `📥 تم تصدير ${typeText} كـ CSV: ${dataToExport.length} عنصر`);
    showAlert(`تم تصدير ${typeText} كـ CSV بنجاح (${dataToExport.length} عنصر)`, 'success');
}

function exportAsJSON(dataToExport, filename, type) {
    // فصل الأفلام والمسلسلات
    const movies = dataToExport.filter(item => item.type === 'movie');
    const series = dataToExport.filter(item => item.type === 'series');

    // الحصول على اسم الموقع
    const siteName = getCurrentSiteName();

    let exportData = {};
    let actualFilename = filename;

    // إنشاء البيانات حسب النوع المطلوب
    if (movies.length > 0 && series.length > 0) {
        // إذا كان هناك أفلام ومسلسلات، إنشاء ملفين منفصلين
        createAndDownloadJSON(movies, `${filename}_أفلام`, type, 'movies', siteName);
        createAndDownloadJSON(series, `${filename}_مسلسلات`, type, 'series', siteName);
        return; // الخروج لأننا أنشأنا ملفين منفصلين
    } else if (movies.length > 0) {
        // أفلام فقط
        exportData = {
            "movies_info": movies.map(item => ({
                "movies_name": item.title || 'غير محدد',
                "movies_img": item.image || 'غير متوفرة',
                "movies_href": item.link || 'غير متوفر'
            }))
        };
        actualFilename = `${siteName}_أفلام_${movies.length}عنصر`;
    } else if (series.length > 0) {
        // مسلسلات فقط
        exportData = {
            "series_info": series.map(item => ({
                "series_name": item.title || 'غير محدد',
                "series_img": item.image || 'غير متوفرة',
                "series_href": item.link || 'غير متوفر'
            }))
        };
        actualFilename = `${siteName}_مسلسلات_${series.length}عنصر`;
    } else {
        showAlert('لا توجد بيانات صالحة للتصدير', 'warning');
        return;
    }

    // تحويل إلى JSON
    const jsonContent = JSON.stringify(exportData, null, 2);

    // تحميل الملف
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${actualFilename}_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // تنظيف الرابط
    URL.revokeObjectURL(url);

    // رسالة نجاح
    const typeText = getTypeText(type);
    addLogEntry('success', `📥 تم تصدير ${typeText} كـ JSON: ${dataToExport.length} عنصر من ${siteName}`);
    showAlert(`تم تصدير ${typeText} كـ JSON بنجاح (${dataToExport.length} عنصر من ${siteName})`, 'success');
}

function createAndDownloadJSON(data, filename, type, dataType, siteName = null) {
    // استخدام اسم الموقع المُمرر أو الحصول عليه
    const currentSiteName = siteName || getCurrentSiteName();

    let exportData = {};
    let actualFilename = filename;

    if (dataType === 'movies') {
        exportData = {
            "movies_info": data.map(item => ({
                "movies_name": item.title || 'غير محدد',
                "movies_img": item.image || 'غير متوفرة',
                "movies_href": item.link || 'غير متوفر'
            }))
        };
        actualFilename = `${currentSiteName}_أفلام_${data.length}عنصر`;
    } else if (dataType === 'series') {
        exportData = {
            "series_info": data.map(item => ({
                "series_name": item.title || 'غير محدد',
                "series_img": item.image || 'غير متوفرة',
                "series_href": item.link || 'غير متوفر'
            }))
        };
        actualFilename = `${currentSiteName}_مسلسلات_${data.length}عنصر`;
    }

    const jsonContent = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${actualFilename}_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // تنظيف الرابط
    URL.revokeObjectURL(url);

    const typeText = getTypeText(type);
    const itemType = dataType === 'movies' ? 'فيلم' : 'مسلسل';
    addLogEntry('success', `📥 تم تصدير ${dataType === 'movies' ? 'الأفلام' : 'المسلسلات'} من ${currentSiteName} كـ JSON: ${data.length} ${itemType}`);
}

function getTypeText(type) {
    switch(type) {
        case 'total': return 'إجمالي المستخرج';
        case 'filtered': return 'النتائج المفلترة';
        case 'excluded': return 'النتائج المستبعدة';
        case 'included': return 'النتائج المختارة';
        default: return 'النتائج';
    }
}

function convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [];

    // إضافة العناوين
    csvRows.push(headers.join(','));

    // إضافة البيانات
    for (const row of data) {
        const values = headers.map(header => {
            const value = row[header] || '';
            // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
            return `"${String(value).replace(/"/g, '""')}"`;
        });
        csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
}

// إعداد مستمعي الأحداث للكلمات الافتراضية
function setupDefaultKeywordsListeners() {
    const checkboxes = document.querySelectorAll('.default-keyword-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // تحديث عدد الكلمات المحددة
            updateSelectedKeywordsCount();

            // إضافة تأثير بصري
            const label = this.nextElementSibling;
            const badge = label.querySelector('.badge');

            if (this.checked) {
                badge.style.transform = 'scale(1.1)';
                badge.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                addLogEntry('info', `✅ تم اختيار الكلمة: ${this.value}`);
            } else {
                badge.style.transform = 'scale(1)';
                badge.style.boxShadow = 'none';
                addLogEntry('info', `❌ تم إلغاء اختيار الكلمة: ${this.value}`);
            }
        });
    });
}

// تحديث عدد الكلمات المحددة
function updateSelectedKeywordsCount() {
    const selectedCount = document.querySelectorAll('.default-keyword-checkbox:checked').length;
    const additionalTerms = document.getElementById('filterSearchTerm').value.trim();

    let totalTerms = selectedCount;
    if (additionalTerms) {
        const additionalCount = additionalTerms.split(',').filter(term => term.trim()).length;
        totalTerms += additionalCount;
    }

    // يمكن إضافة عرض العدد في مكان ما في الواجهة إذا لزم الأمر
}

// الانتقال التلقائي لسجل التطور اللحظي
function scrollToProgressLog() {
    // البحث عن قسم سجل التطور
    const progressLogSection = document.querySelector('h3:has(i.fa-list-alt)');
    const progressLogContainer = document.getElementById('progressLog');

    if (progressLogSection) {
        // انتقال سلس لقسم سجل التطور
        progressLogSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
        });

        // إضافة تأثير بصري لتسليط الضوء على القسم
        const parentSection = progressLogSection.closest('.form-section');
        if (parentSection) {
            parentSection.style.transition = 'all 0.3s ease';
            parentSection.style.backgroundColor = '#e3f2fd';
            parentSection.style.border = '2px solid #2196f3';
            parentSection.style.borderRadius = '8px';

            // إزالة التأثير بعد 3 ثوان
            setTimeout(() => {
                parentSection.style.backgroundColor = '';
                parentSection.style.border = '';
                parentSection.style.borderRadius = '';
            }, 3000);
        }

        addLogEntry('info', '📍 تم الانتقال تلقائياً لسجل التطور اللحظي');
    } else if (progressLogContainer) {
        // إذا لم نجد العنوان، انتقل للحاوية مباشرة
        progressLogContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });

        addLogEntry('info', '📍 تم الانتقال لسجل التطور');
    }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);
