# أداة استخراج بيانات الأفلام من المواقع - Movie Data Scraper

تطبيق ويب متخصص لاستخراج بيانات الأفلام (اسم الفيلم، رابط الفيلم، صورة الفيلم) من صفحات المواقع بطريقة مشابهة لأداة AgentQL Debugger.

## المميزات الرئيسية

### 🎬 استخراج بيانات الأفلام المتقدم
- استعلامات مُعدة مسبقاً للبحث عن الأفلام
- استخراج اسم الفيلم، رابط الفيلم، وصورة الفيلم
- خوارزميات ذكية للعثور على بيانات الأفلام
- معالجة صفحات متعددة تلقائياً

### 🎯 واجهة مستخدم سهلة
- تصميم عصري مع Bootstrap
- واجهة باللغة العربية
- معاينة فورية للاستعلامات
- عرض النتائج في جداول منظمة

### 🛠️ أدوات التصحيح
- مصحح أخطاء مدمج
- عرض الكود المستخرج
- تمييز العناصر المطابقة
- معاينة HTML مع التمييز

### 📊 تصدير متقدم
- تصدير بصيغة JSON منظمة
- تقسيم الملفات حسب العدد المحدد
- تنسيق خاص للأفلام والمسلسلات
- تحميل مباشر للملفات

### 🔄 تحليل أنماط الصفحات
- اكتشاف تلقائي لأنماط الصفحات
- توليد روابط الصفحات التالية
- دعم التسلسل الرقمي
- معالجة صفحات متعددة

## التقنيات المستخدمة

### Backend
- **Python 3.8+**
- **Flask** - إطار عمل الويب
- **BeautifulSoup4** - تحليل HTML
- **requests** - طلبات HTTP
- **lxml** - محلل XML/HTML سريع

### Frontend
- **HTML5/CSS3**
- **JavaScript (ES6+)**
- **Bootstrap 5** - إطار عمل CSS
- **Font Awesome** - الأيقونات

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python app.py
```

### 3. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## كيفية الاستخدام

### 1. إدخال الرابط
```
https://example.com/movies
```

### 2. كتابة الاستعلام
```sql
SELECT title, link, image FROM page WHERE class="movie-card"
```

### 3. تحديد الفلاتر
- نوع المحتوى: أفلام، مسلسلات، أو الكل
- نوع العنصر: روابط، نصوص، صور، أو الكل

### 4. إعداد الصفحات (اختياري)
- الصفحة الأولى: `https://example.com/page/1`
- الصفحة الثانية: `https://example.com/page/2`
- الصفحة الثالثة: `https://example.com/page/3`

### 5. تحليل النمط
اضغط على "تحليل نمط الصفحات" لاكتشاف النمط التلقائي

### 6. بدء الاستخراج
اضغط على "بدء الاستخراج" لجلب البيانات

## صيغة الاستعلامات

### الصيغة الأساسية
```sql
SELECT [الحقول] FROM page WHERE [الشروط]
```

### أمثلة على الاستعلامات

#### استخراج الأفلام
```sql
SELECT title, link, image FROM page WHERE class="movie-item"
```

#### استخراج المسلسلات
```sql
SELECT title, link FROM page WHERE class="series-card"
```

#### استخراج الروابط فقط
```sql
SELECT link FROM page WHERE tag="a"
```

#### استخراج بشروط متعددة
```sql
SELECT title, link, image FROM page WHERE class="content-item" AND tag="div"
```

### الحقول المدعومة
- `title` / `name` / `text` - النصوص
- `link` / `href` / `url` - الروابط
- `image` / `img` / `src` - الصور
- `class` - فئات CSS
- `id` - معرفات العناصر

### الشروط المدعومة
- `class="اسم_الفئة"` - البحث بفئة CSS
- `id="معرف_العنصر"` - البحث بالمعرف
- `tag="اسم_العنصر"` - البحث بنوع العنصر

## تنسيق التصدير

### للأفلام
```json
{
  "movies_info": [
    {
      "movies_name": "اسم الفيلم",
      "movies_img": "رابط الصورة",
      "movies_href": "رابط الفيلم"
    }
  ]
}
```

### للمسلسلات
```json
{
  "series_info": [
    {
      "series_name": "اسم المسلسل",
      "series_img": "رابط الصورة",
      "series_href": "رابط المسلسل"
    }
  ]
}
```

## بنية المشروع

```
web-scraper/
├── app.py                 # التطبيق الرئيسي
├── scraper_classes.py     # كلاسات الاستخراج
├── requirements.txt       # المتطلبات
├── static/
│   ├── style.css         # ملفات التنسيق
│   └── script.js         # ملفات JavaScript
├── templates/
│   └── index.html        # الواجهة الرئيسية
├── exports/              # مجلد الملفات المصدرة
└── README.md            # هذا الملف
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في المستودع.

---

**ملاحظة**: هذا التطبيق مخصص للأغراض التعليمية والبحثية. يرجى احترام شروط الاستخدام للمواقع المستهدفة.
