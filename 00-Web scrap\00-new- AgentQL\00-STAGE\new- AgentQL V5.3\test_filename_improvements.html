<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات أسماء الملفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1000px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .result-box {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        
        .error-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .btn-test {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
            color: white;
        }
        
        .url-input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .url-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .test-title {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .filename-preview {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-weight: bold;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-file-alt text-primary"></i>
                اختبار تحسينات أسماء الملفات
            </h1>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>الهدف:</strong> اختبار وظائف استخراج اسم الموقع وتكوين أسماء الملفات المحسنة
            </div>

            <!-- اختبار استخراج اسم الموقع -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-globe"></i>
                    اختبار استخراج اسم الموقع
                </h3>
                
                <div class="row">
                    <div class="col-md-8">
                        <input type="url" class="form-control url-input" id="testUrl" 
                               placeholder="أدخل رابط الموقع للاختبار (مثل: https://www.egybest.com)">
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-test w-100" onclick="testSiteNameExtraction()">
                            <i class="fas fa-search"></i> اختبار الاستخراج
                        </button>
                    </div>
                </div>
                
                <div id="siteNameResult"></div>
            </div>

            <!-- اختبار تكوين أسماء الملفات -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-file-signature"></i>
                    اختبار تكوين أسماء الملفات
                </h3>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">اسم الموقع:</label>
                        <input type="text" class="form-control" id="siteName" placeholder="egybest">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">عدد الأفلام:</label>
                        <input type="number" class="form-control" id="moviesCount" value="150">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">عدد المسلسلات:</label>
                        <input type="number" class="form-control" id="seriesCount" value="75">
                    </div>
                </div>
                
                <button class="btn btn-test" onclick="testFilenameGeneration()">
                    <i class="fas fa-magic"></i> إنشاء أسماء الملفات
                </button>
                
                <div id="filenameResults"></div>
            </div>

            <!-- اختبار مواقع مختلفة -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-list"></i>
                    اختبار مواقع مختلفة
                </h3>
                
                <p class="text-muted">اختبار سريع لمواقع شائعة:</p>
                
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-test w-100" onclick="testCommonSite('https://www.egybest.com')">
                            EgyBest
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-test w-100" onclick="testCommonSite('https://mycima.tv')">
                            MyCima
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-test w-100" onclick="testCommonSite('https://shahid4u.cc')">
                            Shahid4U
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-test w-100" onclick="testCommonSite('https://akwam.to')">
                            Akwam
                        </button>
                    </div>
                </div>
                
                <div id="commonSitesResults"></div>
            </div>

            <!-- نتائج الاختبار الشامل -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-check-circle"></i>
                    نتائج الاختبار الشامل
                </h3>
                
                <button class="btn btn-test btn-lg" onclick="runFullTest()">
                    <i class="fas fa-play"></i> تشغيل الاختبار الشامل
                </button>
                
                <div id="fullTestResults"></div>
            </div>
        </div>
    </div>

    <script>
        // نسخ وظائف استخراج اسم الموقع من الملف الأصلي
        function extractSiteName(url) {
            try {
                if (!url) return 'unknown_site';
                
                const urlObj = new URL(url);
                let hostname = urlObj.hostname;
                
                // إزالة www. إذا كانت موجودة
                hostname = hostname.replace(/^www\./, '');
                
                // إزالة النطاق العلوي (.com, .net, .org, إلخ)
                const parts = hostname.split('.');
                let siteName = parts.length > 1 ? parts[0] : hostname;
                
                // تنظيف اسم الموقع ليكون مناسباً لاسم الملف
                siteName = siteName
                    .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_') // استبدال الرموز الخاصة بـ _
                    .replace(/_+/g, '_') // دمج عدة _ متتالية
                    .replace(/^_|_$/g, ''); // إزالة _ من البداية والنهاية
                
                return siteName || 'unknown_site';
            } catch (error) {
                console.warn('خطأ في استخراج اسم الموقع:', error);
                return 'unknown_site';
            }
        }

        function testSiteNameExtraction() {
            const url = document.getElementById('testUrl').value.trim();
            const resultDiv = document.getElementById('siteNameResult');
            
            if (!url) {
                resultDiv.innerHTML = '<div class="error-box">يرجى إدخال رابط للاختبار</div>';
                return;
            }
            
            try {
                const siteName = extractSiteName(url);
                resultDiv.innerHTML = `
                    <div class="result-box">
                        <strong>الرابط:</strong> ${url}<br>
                        <strong>اسم الموقع المستخرج:</strong> <span class="text-success">${siteName}</span>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error-box">
                        <strong>خطأ:</strong> ${error.message}
                    </div>
                `;
            }
        }

        function testFilenameGeneration() {
            const siteName = document.getElementById('siteName').value.trim() || 'test_site';
            const moviesCount = parseInt(document.getElementById('moviesCount').value) || 0;
            const seriesCount = parseInt(document.getElementById('seriesCount').value) || 0;
            const resultDiv = document.getElementById('filenameResults');
            
            const timestamp = new Date().toISOString().split('T')[0].replace(/-/g, '') + '_' + 
                             new Date().toTimeString().split(' ')[0].replace(/:/g, '');
            
            let results = '<h5>أسماء الملفات المُنتجة:</h5>';
            
            if (moviesCount > 0) {
                const movieFilename = `${siteName}_أفلام_${moviesCount}عنصر_${timestamp}.json`;
                results += `<div class="filename-preview"><i class="fas fa-film"></i> ${movieFilename}</div>`;
            }
            
            if (seriesCount > 0) {
                const seriesFilename = `${siteName}_مسلسلات_${seriesCount}عنصر_${timestamp}.json`;
                results += `<div class="filename-preview"><i class="fas fa-tv"></i> ${seriesFilename}</div>`;
            }
            
            if (moviesCount > 0 && seriesCount > 0) {
                const allDataFilename = `${siteName}_جميع_البيانات_${moviesCount + seriesCount}عنصر_${timestamp}.json`;
                results += `<div class="filename-preview"><i class="fas fa-database"></i> ${allDataFilename}</div>`;
            }
            
            resultDiv.innerHTML = results;
        }

        function testCommonSite(url) {
            const siteName = extractSiteName(url);
            const resultDiv = document.getElementById('commonSitesResults');
            
            const existingResults = resultDiv.innerHTML;
            const newResult = `
                <div class="result-box">
                    <strong>${url}</strong> → <span class="text-primary">${siteName}</span>
                </div>
            `;
            
            resultDiv.innerHTML = existingResults + newResult;
        }

        function runFullTest() {
            const resultDiv = document.getElementById('fullTestResults');
            resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تشغيل الاختبار...</div>';
            
            setTimeout(() => {
                const testUrls = [
                    'https://www.egybest.com',
                    'https://mycima.tv',
                    'https://shahid4u.cc',
                    'https://akwam.to',
                    'https://www.netflix.com',
                    'https://arabic-movies.site',
                    'invalid-url',
                    ''
                ];
                
                let results = '<h5>نتائج الاختبار الشامل:</h5>';
                let passedTests = 0;
                
                testUrls.forEach((url, index) => {
                    try {
                        const siteName = extractSiteName(url);
                        const isValid = siteName !== 'unknown_site' || !url;
                        
                        results += `
                            <div class="result-box">
                                <strong>اختبار ${index + 1}:</strong> ${url || 'رابط فارغ'}<br>
                                <strong>النتيجة:</strong> ${siteName}
                                <span class="badge ${isValid ? 'bg-success' : 'bg-warning'} ms-2">
                                    ${isValid ? 'نجح' : 'تحذير'}
                                </span>
                            </div>
                        `;
                        
                        if (isValid) passedTests++;
                    } catch (error) {
                        results += `
                            <div class="error-box">
                                <strong>اختبار ${index + 1}:</strong> ${url}<br>
                                <strong>خطأ:</strong> ${error.message}
                            </div>
                        `;
                    }
                });
                
                results += `
                    <div class="alert alert-info mt-3">
                        <strong>ملخص النتائج:</strong> نجح ${passedTests} من ${testUrls.length} اختبار
                    </div>
                `;
                
                resultDiv.innerHTML = results;
            }, 1000);
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            document.getElementById('testUrl').value = 'https://www.egybest.com';
            document.getElementById('siteName').value = 'egybest';
        };
    </script>
</body>
</html>
