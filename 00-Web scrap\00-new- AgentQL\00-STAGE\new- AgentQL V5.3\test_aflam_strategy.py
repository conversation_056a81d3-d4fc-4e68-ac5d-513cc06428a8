#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار استراتيجية aflam.top الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraper_classes import WebScraper, AflamTopStrategy
import json

def test_aflam_strategy():
    """اختبار استراتيجية aflam.top"""
    
    print("🧪 اختبار استراتيجية aflam.top الجديدة")
    print("=" * 50)
    
    # إنشاء كائن الاستخراج
    scraper = WebScraper()
    
    # تعيين الاستراتيجية يدوياً
    success = scraper.set_strategy(3)  # استراتيجية aflam.top
    
    if success:
        print("✅ تم تعيين استراتيجية aflam.top بنجاح")
    else:
        print("❌ فشل في تعيين الاستراتيجية")
        return
    
    # عرض معلومات الاستراتيجيات
    strategy_info = scraper.get_current_strategy_mode()
    print(f"📊 معلومات الاستراتيجيات:")
    print(f"   - الوضع التلقائي: {strategy_info['auto_mode']}")
    print(f"   - الاستراتيجية اليدوية: {strategy_info['manual_strategy']}")
    print(f"   - عدد الاستراتيجيات المتاحة: {len(strategy_info['available_strategies'])}")
    
    # اختبار الاستراتيجية مع موقع aflam.top
    test_url = "https://aflam.top/genre/%d8%a7%d9%83%d8%b4%d9%86/?page=2"
    test_query = "أفلام"
    
    print(f"\n🔍 اختبار الاستخراج من: {test_url}")
    print(f"📝 الاستعلام: {test_query}")
    
    try:
        # تشغيل الاستخراج
        results = scraper.scrape_website(test_url, test_query, max_pages=1, page_load_delay=2)
        
        print(f"\n📊 النتائج:")
        print(f"   - إجمالي العناصر المستخرجة: {len(results)}")
        
        if results:
            # تحليل النتائج
            movies = [item for item in results if item.get('type') == 'movie']
            series = [item for item in results if item.get('type') == 'series']
            
            print(f"   - الأفلام: {len(movies)}")
            print(f"   - المسلسلات: {len(series)}")
            
            # عرض أول 5 نتائج كعينة
            print(f"\n📋 عينة من النتائج (أول 5 عناصر):")
            for i, item in enumerate(results[:5], 1):
                print(f"   {i}. العنوان: {item.get('title', 'غير متوفر')}")
                print(f"      الرابط: {item.get('link', 'غير متوفر')}")
                print(f"      الصورة: {item.get('image', 'غير متوفر')}")
                print(f"      النوع: {item.get('type', 'غير محدد')}")
                print(f"      ---")
            
            # حفظ النتائج في ملف JSON للمراجعة
            output_file = "test_aflam_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ النتائج في: {output_file}")
            
        else:
            print("❌ لم يتم العثور على أي نتائج")
            
    except Exception as e:
        print(f"❌ خطأ في الاستخراج: {str(e)}")
        import traceback
        traceback.print_exc()

def test_strategy_switching():
    """اختبار التبديل بين الاستراتيجيات"""
    
    print("\n🔄 اختبار التبديل بين الاستراتيجيات")
    print("=" * 50)
    
    scraper = WebScraper()
    
    # اختبار الوضع التلقائي
    print("1️⃣ اختبار الوضع التلقائي:")
    scraper.enable_auto_strategy()
    info = scraper.get_current_strategy_mode()
    print(f"   - الوضع التلقائي: {info['auto_mode']}")
    print(f"   - الاستراتيجيات المتاحة: {len(info['available_strategies'])}")
    
    # اختبار تعيين استراتيجيات مختلفة
    strategies_to_test = [1, 2, 3]
    
    for strategy_id in strategies_to_test:
        print(f"\n{strategy_id + 1}️⃣ اختبار الاستراتيجية {strategy_id}:")
        success = scraper.set_strategy(strategy_id)
        
        if success:
            info = scraper.get_current_strategy_mode()
            strategy_name = info['available_strategies'][strategy_id]['name']
            print(f"   ✅ تم تعيين: {strategy_name}")
            print(f"   - الوضع التلقائي: {info['auto_mode']}")
            print(f"   - الاستراتيجية المحددة: {info['manual_strategy']}")
        else:
            print(f"   ❌ فشل في تعيين الاستراتيجية {strategy_id}")
    
    # العودة للوضع التلقائي
    print(f"\n🔄 العودة للوضع التلقائي:")
    scraper.enable_auto_strategy()
    info = scraper.get_current_strategy_mode()
    print(f"   ✅ تم تفعيل الوضع التلقائي: {info['auto_mode']}")

def test_direct_strategy():
    """اختبار الاستراتيجية مباشرة"""
    
    print("\n🎯 اختبار الاستراتيجية مباشرة")
    print("=" * 50)
    
    # إنشاء استراتيجية aflam.top مباشرة
    strategy = AflamTopStrategy()
    
    print(f"📋 معلومات الاستراتيجية:")
    print(f"   - الاسم: {strategy.name}")
    print(f"   - الوصف: {strategy.description}")
    
    # يمكن إضافة اختبارات أخرى هنا إذا لزم الأمر

if __name__ == "__main__":
    print("🚀 بدء اختبار نظام الاستراتيجيات الجديد")
    print("=" * 60)
    
    try:
        # اختبار التبديل بين الاستراتيجيات
        test_strategy_switching()
        
        # اختبار الاستراتيجية مباشرة
        test_direct_strategy()
        
        # اختبار استراتيجية aflam.top
        test_aflam_strategy()
        
        print("\n✅ اكتملت جميع الاختبارات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
