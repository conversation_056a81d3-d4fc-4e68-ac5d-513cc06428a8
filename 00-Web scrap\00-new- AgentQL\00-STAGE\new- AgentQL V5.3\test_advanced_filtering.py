#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لميزات الفلترة المتقدمة الجديدة
Test Advanced Filtering Features
"""

import requests
import json
import time

def test_advanced_filtering():
    """اختبار ميزات الفلترة المتقدمة"""
    
    print("🚀 بدء اختبار ميزات الفلترة المتقدمة")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # بيانات تجريبية للاختبار
    test_results = [
        {
            "title": "فيلم أكشن مثير",
            "link": "http://example.com/action1",
            "image": "http://example.com/image1.jpg",
            "type": "movie"
        },
        {
            "title": "كوميديا رومانسية",
            "link": "http://example.com/comedy1",
            "image": "لا توجد صورة",
            "type": "movie"
        },
        {
            "title": "فيلم رعب مخيف",
            "link": "http://example.com/horror1",
            "image": "http://example.com/image2.jpg",
            "type": "movie"
        },
        {
            "title": "مسلسل أكشن",
            "link": "http://example.com/series1",
            "image": "",
            "type": "series"
        },
        {
            "title": "دراما عائلية",
            "link": "http://example.com/drama1",
            "image": "http://example.com/image3.jpg",
            "type": "movie"
        },
        {
            "title": "كوميديا ساخرة",
            "link": "http://example.com/comedy2",
            "image": "غير متوفرة",
            "type": "movie"
        },
        {
            "title": "فيلم خيال علمي",
            "link": "http://example.com/scifi1",
            "image": "N/A",
            "type": "movie"
        },
        {
            "title": "مغامرات أكشن",
            "link": "http://example.com/adventure1",
            "image": "http://example.com/image4.jpg",
            "type": "movie"
        }
    ]
    
    print(f"📊 عدد العناصر التجريبية: {len(test_results)}")
    print()
    
    # اختبار 1: فلترة بالاسم - استبعاد
    print("🧪 اختبار 1: فلترة بالاسم - استبعاد الأفلام المحتوية على 'أكشن'")
    
    # محاكاة الفلترة بالاسم
    keywords = ['أكشن']
    excluded_by_name = [item for item in test_results 
                       if any(keyword in item['title'].lower() for keyword in keywords)]
    filtered_by_name = [item for item in test_results 
                       if not any(keyword in item['title'].lower() for keyword in keywords)]
    
    print(f"   النتيجة: تم استبعاد {len(excluded_by_name)} عنصر")
    print(f"   المتبقي: {len(filtered_by_name)} عنصر")
    print(f"   المستبعدة: {[item['title'] for item in excluded_by_name]}")
    print()
    
    # اختبار 2: فلترة بالاسم - اختيار
    print("🧪 اختبار 2: فلترة بالاسم - اختيار الأفلام المحتوية على 'كوميديا'")
    
    keywords = ['كوميديا']
    included_by_name = [item for item in test_results 
                       if any(keyword in item['title'].lower() for keyword in keywords)]
    
    print(f"   النتيجة: تم اختيار {len(included_by_name)} عنصر")
    print(f"   المختارة: {[item['title'] for item in included_by_name]}")
    print()
    
    # اختبار 3: فلترة بالاسم - كلمات متعددة
    print("🧪 اختبار 3: فلترة بالاسم - كلمات متعددة 'أكشن, كوميديا'")
    
    keywords = ['أكشن', 'كوميديا']
    multi_included = [item for item in test_results 
                     if any(keyword in item['title'].lower() for keyword in keywords)]
    
    print(f"   النتيجة: تم اختيار {len(multi_included)} عنصر")
    print(f"   المختارة: {[item['title'] for item in multi_included]}")
    print()
    
    # اختبار 4: فلترة بالصور - استبعاد العناصر بدون صور
    print("🧪 اختبار 4: فلترة بالصور - استبعاد العناصر بدون صور")
    
    no_image_indicators = [
        'لا توجد صورة',
        'لا توجد لها صورة', 
        'no image',
        'no picture',
        'صورة غير متوفرة',
        'غير متوفرة',
        'N/A',
        'n/a',
        'null',
        'undefined',
        ''
    ]
    
    excluded_by_image = [item for item in test_results 
                        if not item.get('image') or 
                        any(indicator.lower() in item['image'].lower() 
                           for indicator in no_image_indicators)]
    
    filtered_by_image = [item for item in test_results 
                        if item.get('image') and 
                        not any(indicator.lower() in item['image'].lower() 
                               for indicator in no_image_indicators)]
    
    print(f"   النتيجة: تم استبعاد {len(excluded_by_image)} عنصر بدون صورة")
    print(f"   المتبقي: {len(filtered_by_image)} عنصر")
    print(f"   المستبعدة: {[item['title'] for item in excluded_by_image]}")
    print()
    
    # اختبار 5: فلترة مركبة - بالاسم والصور معاً
    print("🧪 اختبار 5: فلترة مركبة - استبعاد 'أكشن' + العناصر بدون صور")
    
    # أولاً استبعاد بالاسم
    step1_filtered = [item for item in test_results 
                     if 'أكشن' not in item['title'].lower()]
    
    # ثم استبعاد بالصور
    final_filtered = [item for item in step1_filtered 
                     if item.get('image') and 
                     not any(indicator.lower() in item['image'].lower() 
                            for indicator in no_image_indicators)]
    
    total_excluded = len(test_results) - len(final_filtered)
    
    print(f"   النتيجة: تم استبعاد {total_excluded} عنصر إجمالي")
    print(f"   المتبقي: {len(final_filtered)} عنصر")
    print(f"   المتبقية: {[item['title'] for item in final_filtered]}")
    print()
    
    # اختبار 6: إحصائيات مفصلة
    print("🧪 اختبار 6: إحصائيات مفصلة")
    
    total_movies = len([item for item in test_results if item['type'] == 'movie'])
    total_series = len([item for item in test_results if item['type'] == 'series'])
    items_with_images = len([item for item in test_results 
                           if item.get('image') and 
                           not any(indicator.lower() in item['image'].lower() 
                                  for indicator in no_image_indicators)])
    items_without_images = len(test_results) - items_with_images
    
    print(f"   📊 إجمالي العناصر: {len(test_results)}")
    print(f"   🎬 الأفلام: {total_movies}")
    print(f"   📺 المسلسلات: {total_series}")
    print(f"   🖼️ عناصر بصور: {items_with_images}")
    print(f"   🚫 عناصر بدون صور: {items_without_images}")
    print()
    
    # اختبار 7: اختبار أداء الفلترة
    print("🧪 اختبار 7: اختبار أداء الفلترة")
    
    start_time = time.time()
    
    # تشغيل عدة عمليات فلترة
    for i in range(100):
        # فلترة بالاسم
        temp_filtered = [item for item in test_results 
                        if 'فيلم' in item['title'].lower()]
        
        # فلترة بالصور
        temp_filtered2 = [item for item in test_results 
                         if item.get('image') and 
                         'http' in item['image'].lower()]
    
    end_time = time.time()
    processing_time = (end_time - start_time) * 1000  # بالمللي ثانية
    
    print(f"   ⏱️ وقت معالجة 100 عملية فلترة: {processing_time:.2f} مللي ثانية")
    print(f"   📈 متوسط الوقت لكل عملية: {processing_time/100:.2f} مللي ثانية")
    print()
    
    print("✅ تم إنجاز جميع اختبارات الفلترة المتقدمة بنجاح!")
    print()
    
    # ملخص النتائج
    print("📋 ملخص نتائج الاختبار:")
    print("=" * 30)
    print("✅ فلترة بالاسم - استبعاد: نجح")
    print("✅ فلترة بالاسم - اختيار: نجح") 
    print("✅ فلترة بكلمات متعددة: نجح")
    print("✅ فلترة بالصور: نجح")
    print("✅ فلترة مركبة: نجح")
    print("✅ إحصائيات مفصلة: نجح")
    print("✅ اختبار الأداء: نجح")
    print()
    print("🎉 جميع ميزات الفلترة المتقدمة تعمل بشكل مثالي!")

if __name__ == "__main__":
    test_advanced_filtering()
