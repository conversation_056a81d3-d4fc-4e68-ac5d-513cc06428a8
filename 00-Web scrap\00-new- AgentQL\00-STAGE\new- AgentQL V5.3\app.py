from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import threading
import time
from scraper_classes import WebScraper, PagePatternA<PERSON>yzer, QueryDebugger, DataExporter

app = Flask(__name__)
CORS(app)

# إعدادات الترميز العربي
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True

# إعداد مجلد التصدير
EXPORT_FOLDER = 'exports'
if not os.path.exists(EXPORT_FOLDER):
    os.makedirs(EXPORT_FOLDER)

# متغيرات التحكم في العمليات
scraping_control = {
    'is_running': False,
    'is_paused': False,
    'should_stop': False,
    'current_results': [],
    'current_thread': None
}

@app.route('/')
def index():
    return render_template('index.html')

import threading
import time

def scrape_single_page_with_progress(scraper, url, query, page_load_delay):
    """استخراج صفحة واحدة مع تحديث العداد اللحظي"""
    print(f"🔍 بدء استخراج صفحة واحدة: {url}")

    # تحديث الحالة - بدء العملية
    scraping_control['current_results'] = []
    scraping_control['summary'] = {'movies_count': 0, 'series_count': 0, 'total_count': 0}

    # استخراج البيانات
    results = scraper.scrape_website(url, query, max_pages=1, page_load_delay=page_load_delay)

    # تحديث النتائج تدريجياً لمحاكاة التقدم
    for i, item in enumerate(results):
        if scraping_control['should_stop']:
            break

        # تحديث العداد
        current_results = results[:i+1]
        movies = [item for item in current_results if item.get('type') == 'movie']
        series = [item for item in current_results if item.get('type') == 'series']

        scraping_control['current_results'] = current_results
        scraping_control['summary'] = {
            'movies_count': len(movies),
            'series_count': len(series),
            'total_count': len(current_results)
        }

        # تأخير قصير لإظهار التقدم
        time.sleep(0.5)

    return results

def scrape_multiple_pages_with_progress(scraper, pattern_info, query, max_pages, start_page, page_load_delay):
    """استخراج صفحات متعددة مع تحديث العداد اللحظي"""
    print(f"🔍 بدء استخراج {max_pages} صفحة من الصفحة {start_page}")
    print(f"📊 معلومات النمط: {pattern_info}")

    # التحقق من صحة pattern_info
    if not pattern_info or 'pattern' not in pattern_info:
        print("❌ معلومات النمط غير صحيحة")
        return [], {'movies_count': 0, 'series_count': 0, 'total_count': 0}

    # تحديث الحالة - بدء العملية
    scraping_control['current_results'] = []
    scraping_control['summary'] = {'movies_count': 0, 'series_count': 0, 'total_count': 0}
    scraping_control['current_page'] = start_page  # إضافة تتبع الصفحة الحالية

    # استخدام الدالة الأصلية مع تحديث مستمر للعداد
    try:
        # متغير لتتبع النتائج المؤقتة
        temp_results = []
        current_page_num = start_page

        # استخدام scrape_multiple_pages الأصلية مع callback للتحديث
        def progress_callback(message):
            nonlocal current_page_num
            print(f"📊 {message}")

            # تحديث رقم الصفحة الحالية
            if "معالجة الصفحة" in message:
                try:
                    import re
                    page_match = re.search(r'معالجة الصفحة (\d+)', message)
                    if page_match:
                        current_page_num = int(page_match.group(1))
                        scraping_control['current_page'] = current_page_num
                        print(f"📄 تحديث الصفحة الحالية: {current_page_num}")
                except:
                    pass

            # إذا كانت الرسالة تحتوي على معلومات النتائج، نحدث العداد
            if "تم استخراج" in message and "عنصر" in message:
                # محاولة استخراج عدد العناصر من الرسالة
                try:
                    import re
                    match = re.search(r'تم استخراج (\d+) عنصر', message)
                    if match:
                        new_items_count = int(match.group(1))
                        # تحديث العداد التقديري
                        temp_results.extend([{'type': 'movie'}] * new_items_count)  # مؤقت للعداد

                        movies = [item for item in temp_results if item.get('type') == 'movie']
                        series = [item for item in temp_results if item.get('type') == 'series']

                        scraping_control['current_results'] = temp_results
                        scraping_control['summary'] = {
                            'movies_count': len(movies),
                            'series_count': len(series),
                            'total_count': len(temp_results)
                        }
                        scraping_control['current_page'] = current_page_num
                except:
                    pass

        # استدعاء الدالة الأصلية
        results, summary = scraper.scrape_multiple_pages(
            pattern_info, query, max_pages, start_page,
            progress_callback=progress_callback,
            page_load_delay=page_load_delay
        )

        # تحديث النتائج النهائية
        scraping_control['current_results'] = results
        scraping_control['summary'] = summary
        scraping_control['current_page'] = current_page_num

        print(f"✅ اكتمل الاستخراج المتتالي: {len(results)} عنصر")
        return results, summary

    except Exception as e:
        print(f"❌ خطأ في الاستخراج المتتالي: {str(e)}")
        return [], {'movies_count': 0, 'series_count': 0, 'total_count': 0}

def controlled_scraping_worker(scraper, url, query, max_pages, page_load_delay, scraping_mode, pattern_info, start_page):
    """دالة العمل للاستخراج مع التحكم"""
    try:
        # تعيين حالة البدء
        scraping_control['is_running'] = True
        scraping_control['should_stop'] = False
        scraping_control['current_results'] = []

        print(f"🚀 بدء عملية الاستخراج المتحكم بها")

        if scraping_mode == 'multi' and pattern_info:
            # الاستخراج المتتالي مع تحديث مستمر
            results, summary = scrape_multiple_pages_with_progress(
                scraper, pattern_info, query, max_pages, start_page, page_load_delay
            )

            scraping_control['current_results'] = results
            scraping_control['summary'] = summary

        else:
            # الاستخراج من صفحة واحدة مع تحديث مستمر
            results = scrape_single_page_with_progress(
                scraper, url, query, page_load_delay
            )

            # عد الأفلام والمسلسلات
            movies = [item for item in results if item.get('type') == 'movie']
            series = [item for item in results if item.get('type') == 'series']

            scraping_control['current_results'] = results
            scraping_control['summary'] = {
                'movies_count': len(movies),
                'series_count': len(series),
                'total_count': len(results)
            }

        print(f"✅ اكتملت عملية الاستخراج: {len(scraping_control['current_results'])} عنصر")

    except Exception as e:
        print(f"❌ خطأ في عملية الاستخراج: {str(e)}")
        scraping_control['error'] = str(e)
    finally:
        scraping_control['is_running'] = False

@app.route('/api/scrape', methods=['POST'])
def scrape_data():
    """API endpoint لاستخراج بيانات الأفلام والمسلسلات مع التحكم"""
    try:
        # التحقق من وجود عملية جارية
        if scraping_control['is_running']:
            return jsonify({
                'success': False,
                'error': 'يوجد عملية استخراج جارية بالفعل'
            }), 400

        data = request.json
        url = data.get('url')
        query = data.get('query')
        max_pages = data.get('max_pages', 1)
        max_items_per_file = data.get('max_items_per_file', 100)
        scraping_mode = data.get('scraping_mode', 'single')
        pattern_info = data.get('pattern_info')
        start_page = data.get('start_page', 1)
        page_load_delay = data.get('page_load_delay', 2)

        if not query:
            return jsonify({
                'success': False,
                'error': 'يرجى إدخال الاستعلام'
            }), 400

        if scraping_mode == 'single' and not url:
            return jsonify({
                'success': False,
                'error': 'يرجى إدخال الرابط'
            }), 400

        # إنشاء كائن الاستخراج
        scraper = WebScraper()

        # بدء العملية في thread منفصل
        thread = threading.Thread(
            target=controlled_scraping_worker,
            args=(scraper, url, query, max_pages, page_load_delay, scraping_mode, pattern_info, start_page)
        )

        scraping_control['current_thread'] = thread
        thread.start()

        # انتظار قصير للتأكد من بدء العملية
        time.sleep(0.5)

        return jsonify({
            'success': True,
            'message': 'تم بدء عملية الاستخراج',
            'status': 'started'
        })

    except Exception as e:
        print(f"❌ خطأ في بدء الاستخراج: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scrape_status', methods=['GET'])
def get_scrape_status():
    """الحصول على حالة عملية الاستخراج"""
    try:
        current_results = scraping_control.get('current_results', [])
        summary = scraping_control.get('summary', {})

        status = {
            'is_running': scraping_control['is_running'],
            'is_paused': scraping_control['is_paused'],
            'should_stop': scraping_control['should_stop'],
            'results_count': len(current_results),
            'has_results': len(current_results) > 0,
            'movies_count': summary.get('movies_count', 0),
            'series_count': summary.get('series_count', 0),
            'current_page': scraping_control.get('current_page', 1)  # إضافة رقم الصفحة الحالية
        }

        # إذا اكتملت العملية، إرجاع النتائج
        if not scraping_control['is_running'] and scraping_control.get('current_results'):
            results = scraping_control['current_results']
            summary = scraping_control.get('summary', {})

            status.update({
                'completed': True,
                'data': results,
                'total_items': len(results),
                'movies_count': summary.get('movies_count', 0),
                'series_count': summary.get('series_count', 0),
                'summary': summary
            })

        # إذا كان هناك خطأ
        if scraping_control.get('error'):
            status.update({
                'error': scraping_control['error'],
                'completed': True
            })
            # مسح الخطأ بعد إرساله
            scraping_control.pop('error', None)

        return jsonify(status)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analyze_pages', methods=['POST'])
def analyze_pages():
    """تحليل أنماط الصفحات"""
    try:
        data = request.json
        pages = data.get('pages', [])
        
        analyzer = PagePatternAnalyzer()
        pattern = analyzer.analyze_pattern(pages)
        
        return jsonify({
            'success': True,
            'pattern': pattern
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debug', methods=['POST'])
def debug_query():
    """Debugger للاستعلامات"""
    try:
        data = request.json
        url = data.get('url')
        query = data.get('query')
        
        debugger = QueryDebugger()
        debug_info = debugger.debug_query(url, query)
        
        return jsonify({
            'success': True,
            'debug_info': debug_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export', methods=['POST'])
def export_data():
    """تصدير البيانات بصيغة JSON مع خيارات متقدمة"""
    try:
        data = request.json
        results = data.get('results', [])
        content_type = data.get('content_type', 'movies')
        max_items_per_file = data.get('max_items_per_file', 100)
        export_type = data.get('export_type', 'multiple')  # single, multiple, separate
        export_format = data.get('export_format', 'json')  # json, csv, xlsx
        site_name = data.get('site_name', None)  # اسم الموقع

        if not results:
            return jsonify({'success': False, 'error': 'لا توجد بيانات للتصدير'})

        print(f"📤 بدء تصدير {len(results)} عنصر...")
        print(f"📊 نوع التصدير: {export_type}")
        print(f"📄 تنسيق الملف: {export_format}")
        if site_name:
            print(f"🌐 اسم الموقع: {site_name}")

        exporter = DataExporter(EXPORT_FOLDER)

        # تحديد طريقة التصدير
        if export_type == 'single':
            # ملف واحد مجمع
            files = exporter.export_to_single_file(results, export_format, site_name)
        elif export_type == 'separate':
            # ملفات منفصلة للأفلام والمسلسلات
            files = exporter.export_to_separate_files(results, max_items_per_file, export_format, site_name)
        else:
            # ملفات متعددة (افتراضي)
            files = exporter.export_to_json(results, max_items_per_file, site_name)

        print(f"✅ تم إنشاء {len(files)} ملف بنجاح")

        return jsonify({
            'success': True,
            'files': files,
            'total_files': len(files),
            'export_type': export_type,
            'export_format': export_format,
            'message': f'تم تصدير البيانات إلى {len(files)} ملف'
        })

    except Exception as e:
        print(f"❌ خطأ في التصدير: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/control_scraping', methods=['POST'])
def control_scraping():
    """التحكم في عملية الاستخراج (إيقاف/استئناف/إيقاف مع تصدير)"""
    try:
        data = request.json
        action = data.get('action')  # pause, resume, stop_and_export

        if action == 'pause':
            scraping_control['is_paused'] = True
            return jsonify({
                'success': True,
                'message': 'تم إيقاف العملية مؤقتاً',
                'status': 'paused'
            })

        elif action == 'resume':
            scraping_control['is_paused'] = False
            return jsonify({
                'success': True,
                'message': 'تم استئناف العملية',
                'status': 'running'
            })

        elif action == 'stop_and_export':
            # إيقاف العملية فوراً
            scraping_control['should_stop'] = True
            scraping_control['is_paused'] = False
            scraping_control['is_running'] = False

            # انتظار قصير للسماح للعملية بالتوقف
            time.sleep(0.5)

            # تصدير النتائج الحالية
            current_results = scraping_control.get('current_results', [])
            if current_results:
                exporter = DataExporter(EXPORT_FOLDER)
                files = exporter.export_to_json(current_results, 100)

                return jsonify({
                    'success': True,
                    'message': f'تم إيقاف العملية وتصدير {len(current_results)} عنصر',
                    'status': 'stopped',
                    'exported_files': files,
                    'total_items': len(current_results),
                    'data': current_results,
                    'movies_count': len([item for item in current_results if item.get('type') == 'movie']),
                    'series_count': len([item for item in current_results if item.get('type') == 'series'])
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'تم إيقاف العملية - لا توجد نتائج للتصدير',
                    'status': 'stopped'
                })

        else:
            return jsonify({
                'success': False,
                'error': 'إجراء غير صحيح'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/filter_results', methods=['POST'])
def filter_results():
    """تصفية وتحليل النتائج"""
    try:
        data = request.json
        results = data.get('results', [])
        filter_type = data.get('filter_type')  # exclude, include, show_excluded, show_included
        search_term = data.get('search_term', '').strip().lower()

        if not search_term:
            return jsonify({
                'success': False,
                'error': 'يرجى إدخال كلمة البحث'
            }), 400

        filtered_results = []
        excluded_results = []
        included_results = []

        for item in results:
            title = item.get('title', '').lower()

            if search_term in title:
                included_results.append(item)
            else:
                excluded_results.append(item)

        if filter_type == 'exclude':
            # استبعاد الأفلام المحتوية على الكلمة
            filtered_results = excluded_results
            message = f'تم استبعاد {len(included_results)} فيلم يحتوي على "{search_term}"'

        elif filter_type == 'include':
            # اختيار الأفلام المحتوية على الكلمة فقط
            filtered_results = included_results
            message = f'تم اختيار {len(included_results)} فيلم يحتوي على "{search_term}"'

        elif filter_type == 'show_excluded':
            # عرض الأفلام المستبعدة
            filtered_results = excluded_results
            message = f'عرض {len(excluded_results)} فيلم مستبعد'

        elif filter_type == 'show_included':
            # عرض الأفلام المختارة
            filtered_results = included_results
            message = f'عرض {len(included_results)} فيلم مختار'

        else:
            return jsonify({
                'success': False,
                'error': 'نوع التصفية غير صحيح'
            }), 400

        return jsonify({
            'success': True,
            'filtered_results': filtered_results,
            'total_filtered': len(filtered_results),
            'total_excluded': len(excluded_results),
            'total_included': len(included_results),
            'message': message,
            'search_term': search_term,
            'filter_type': filter_type
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/strategies', methods=['GET'])
def get_strategies():
    """الحصول على قائمة الاستراتيجيات المتاحة"""
    try:
        scraper = WebScraper()
        strategies_info = scraper.get_current_strategy_mode()

        return jsonify({
            'success': True,
            'strategies_info': strategies_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/set_strategy', methods=['POST'])
def set_strategy():
    """تعيين استراتيجية محددة"""
    try:
        data = request.json
        strategy_id = data.get('strategy_id')

        if strategy_id == 'auto':
            # تفعيل الوضع التلقائي
            scraper = WebScraper()
            scraper.enable_auto_strategy()

            return jsonify({
                'success': True,
                'message': 'تم تفعيل الوضع التلقائي',
                'mode': 'auto'
            })

        elif strategy_id and isinstance(strategy_id, int):
            # تعيين استراتيجية محددة
            scraper = WebScraper()
            success = scraper.set_strategy(strategy_id)

            if success:
                strategies = scraper.get_available_strategies()
                strategy_name = strategies.get(strategy_id, {}).get('name', 'غير معروف')

                return jsonify({
                    'success': True,
                    'message': f'تم تعيين الاستراتيجية: {strategy_name}',
                    'mode': 'manual',
                    'strategy_id': strategy_id
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'استراتيجية غير صحيحة'
                }), 400

        else:
            return jsonify({
                'success': False,
                'error': 'معرف الاستراتيجية غير صحيح'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/exports/<filename>')
def download_file(filename):
    """تحميل الملفات المصدرة"""
    try:
        return send_file(os.path.join(EXPORT_FOLDER, filename), as_attachment=True)
    except Exception as e:
        return jsonify({'error': str(e)}), 404

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم استخراج البيانات...")
    print("🌐 الخادم متاح على: http://localhost:5000")
    print("📝 مع دعم الترميز العربي ونظام التأخير المحسن")
    app.run(debug=True, host='0.0.0.0', port=5000)
