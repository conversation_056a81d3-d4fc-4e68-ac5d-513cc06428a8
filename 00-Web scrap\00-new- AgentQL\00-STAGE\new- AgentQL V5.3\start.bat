@echo off
chcp 65001 >nul
title أداة استخراج بيانات الأفلام من المواقع

echo.
echo ===============================================
echo 🎬  أداة استخراج بيانات الأفلام من المواقع
echo ===============================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

echo.
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل التطبيق...
echo 🌐 سيتم فتح التطبيق على: http://127.0.0.1:5000
echo 📝 للإيقاف: اضغط Ctrl+C
echo.

python run.py

echo.
echo ✅ تم إيقاف التطبيق
pause
