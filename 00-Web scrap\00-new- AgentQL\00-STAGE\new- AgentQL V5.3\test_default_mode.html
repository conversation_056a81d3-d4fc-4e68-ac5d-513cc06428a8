<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوضع الافتراضي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white text-center">
                        <h2><i class="fas fa-check-circle"></i> اختبار الوضع الافتراضي</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- اختبار الوضع الافتراضي -->
                        <div class="form-section mb-4">
                            <h3><i class="fas fa-cog"></i> اختيار وضع الاستخراج</h3>
                            <div class="form-group">
                                <div class="scraping-mode-selector">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="scrapingMode" id="singlePageMode" value="single">
                                        <label class="form-check-label" for="singlePageMode">
                                            <i class="fas fa-file"></i> صفحة منفردة
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="scrapingMode" id="multiPageMode" value="multi" checked>
                                        <label class="form-check-label" for="multiPageMode">
                                            <i class="fas fa-files"></i> صفحات متتالية
                                        </label>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    ✅ الوضع الافتراضي الآن هو "صفحات متتالية"
                                </small>
                            </div>
                        </div>

                        <!-- قسم الصفحة المنفردة -->
                        <div class="form-section" id="singlePageSection" style="display: none;">
                            <div class="alert alert-warning">
                                <h4><i class="fas fa-file"></i> وضع الصفحة المنفردة</h4>
                                <p>هذا القسم يظهر عند اختيار "صفحة منفردة"</p>
                            </div>
                            <div class="form-group">
                                <label for="url">رابط الموقع المطلوب استخراج الأفلام منه:</label>
                                <input type="url" id="url" class="form-control" placeholder="https://movies-site.com/movies">
                                <small class="form-text text-muted">
                                    أدخل رابط الصفحة التي تحتوي على قائمة الأفلام
                                </small>
                            </div>
                        </div>

                        <!-- قسم الصفحات المتتالية -->
                        <div class="form-section" id="multiPageSection">
                            <div class="alert alert-success">
                                <h4><i class="fas fa-files"></i> وضع الصفحات المتتالية (الوضع الافتراضي)</h4>
                                <p>هذا القسم يظهر عند اختيار "صفحات متتالية" - وهو الوضع الافتراضي الآن</p>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>كيفية الاستخدام:</strong> أدخل 3 صفحات متتالية لاستنتاج النمط تلقائياً، ثم حدد عدد الصفحات المطلوب استخراجها.
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="page1">الصفحة الأولى: <span class="text-danger">*</span></label>
                                        <input type="url" id="page1" class="form-control" placeholder="https://example.com/page/1">
                                        <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="page2">الصفحة الثانية: <span class="text-danger">*</span></label>
                                        <input type="url" id="page2" class="form-control" placeholder="https://example.com/page/2">
                                        <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="page3">الصفحة الثالثة: <span class="text-danger">*</span></label>
                                        <input type="url" id="page3" class="form-control" placeholder="https://example.com/page/3">
                                        <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="maxPages">عدد الصفحات المطلوب استخراجها:</label>
                                        <input type="number" id="maxPages" class="form-control" value="10" min="1" max="100">
                                        <small class="form-text text-muted">العدد الإجمالي للصفحات المراد استخراجها</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="startFromPage">البدء من الصفحة رقم:</label>
                                        <input type="number" id="startFromPage" class="form-control" value="1" min="1">
                                        <small class="form-text text-muted">رقم الصفحة للبدء منها</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- منطقة الاختبار -->
                        <div class="form-section mt-4">
                            <h3><i class="fas fa-test-tube"></i> نتائج الاختبار</h3>
                            <div id="testResults" class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 
                                <strong>حالة الاختبار:</strong> 
                                <span id="testStatus">جاري التحقق من الوضع الافتراضي...</span>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <button id="testDefaultBtn" class="btn btn-primary btn-block">
                                        <i class="fas fa-play"></i> اختبار الوضع الافتراضي
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button id="toggleModeBtn" class="btn btn-secondary btn-block">
                                        <i class="fas fa-exchange-alt"></i> تبديل الوضع
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة التبديل بين الأوضاع
        function toggleScrapingMode() {
            const singlePageMode = document.getElementById('singlePageMode').checked;
            const multiPageMode = document.getElementById('multiPageMode').checked;
            const singlePageSection = document.getElementById('singlePageSection');
            const multiPageSection = document.getElementById('multiPageSection');

            if (singlePageMode) {
                singlePageSection.style.display = 'block';
                multiPageSection.style.display = 'none';
                updateTestStatus('تم التبديل إلى وضع الصفحة المنفردة', 'warning');
            } else if (multiPageMode) {
                singlePageSection.style.display = 'none';
                multiPageSection.style.display = 'block';
                updateTestStatus('تم التبديل إلى وضع الصفحات المتتالية (الوضع الافتراضي)', 'success');
            }
        }

        // تحديث حالة الاختبار
        function updateTestStatus(message, type) {
            const testResults = document.getElementById('testResults');
            const testStatus = document.getElementById('testStatus');
            
            testResults.className = `alert alert-${type}`;
            testStatus.textContent = message;
        }

        // اختبار الوضع الافتراضي
        function testDefaultMode() {
            const multiPageMode = document.getElementById('multiPageMode').checked;
            const multiPageSection = document.getElementById('multiPageSection');
            const singlePageSection = document.getElementById('singlePageSection');
            
            if (multiPageMode && multiPageSection.style.display !== 'none') {
                updateTestStatus('✅ الاختبار نجح! الوضع الافتراضي هو "صفحات متتالية" والقسم المناسب معروض', 'success');
            } else {
                updateTestStatus('❌ الاختبار فشل! هناك مشكلة في الوضع الافتراضي', 'danger');
            }
        }

        // تبديل الوضع للاختبار
        function toggleModeForTest() {
            const singlePageMode = document.getElementById('singlePageMode');
            const multiPageMode = document.getElementById('multiPageMode');
            
            if (multiPageMode.checked) {
                singlePageMode.checked = true;
                multiPageMode.checked = false;
            } else {
                singlePageMode.checked = false;
                multiPageMode.checked = true;
            }
            
            toggleScrapingMode();
        }

        // ربط الأحداث
        document.getElementById('singlePageMode').addEventListener('change', toggleScrapingMode);
        document.getElementById('multiPageMode').addEventListener('change', toggleScrapingMode);
        document.getElementById('testDefaultBtn').addEventListener('click', testDefaultMode);
        document.getElementById('toggleModeBtn').addEventListener('click', toggleModeForTest);

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق الوضع الافتراضي
            toggleScrapingMode();
            
            // اختبار تلقائي بعد ثانية واحدة
            setTimeout(testDefaultMode, 1000);
            
            console.log('✅ تم تحميل صفحة اختبار الوضع الافتراضي');
            console.log('🔧 الوضع الافتراضي: صفحات متتالية');
        });
    </script>
</body>
</html>
