اريد انشاء تطبيق ويب يعمل كأداة لاستخراج البيانات من صفحات المواقع (Web Scraping) 
بطريقة مشابهة لأداة AgentQL Debugger.  

مواصفات التطبيق:  
1- لغة البرمجة: Python + Flask (أو FastAPI).  
2- واجهة أمامية: React أو HTML/JavaScript بسيطة مع Bootstrap أو TailwindCSS.  
3- الوظائف الأساسية:  
   - إدخال رابط الموقع المطلوب.  
   - إدخال استعلام (Query) بلغة بسيطة شبيهة بـ SQL/GraphQL، مثل:
       SELECT links FROM page WHERE class="movie-card"
   - تنفيذ الاستعلام واستخراج العناصر المطلوبة من الـ DOM (HTML).  
   - عرض النتائج في شكل جدول أو JSON.  
4- دعم فلاتر إضافية:  
   - اختيار استخراج (أفلام فقط - مسلسلات فقط).  
   - اختيار العنصر المطلوب (روابط فقط - نصوص فقط - صور فقط).  
   - اختيار نطاق (صفحة واحدة / عدة صفحات بتسلسل).  
5- التعامل مع الصفحات:  
   - وجود خانة لإدخال الصفحة الأولى.  
   - خانة لإدخال الصفحة الثانية (اختياري).  
   - خانة لإدخال الصفحة الثالثة (اختياري).  
   - التطبيق يقوم بتحليل الروابط المدخلة واستخراج **النمط** الخاص بالصفحات التالية تلقائياً.  
   - وجود خانة لتحديد **عدد الصفحات المستهدفة** لجلب البيانات منها.  
6- إدارة التصدير بصيغة JSON:  
   - في حالة الأفلام:
     {
       "movies_info": [
         {
           "movies_name": "اسم الفيلم",
           "movies_img": "رابط الصورة",
           "movies_href": "رابط الفيلم"
         }
       ]
     }

   - في حالة المسلسلات:
     {
       "series_info": [
         {
           "series_name": "اسم المسلسل",
           "series_img": "رابط الصورة",
           "series_href": "رابط المسلسل"
         }
       ]
     }

   - إضافة خانة لتحديد **العدد الأقصى للأفلام أو المسلسلات لكل ملف JSON**.  
   - إذا تم تجاوز العدد، يقوم التطبيق تلقائياً بتقسيم النتائج على عدة ملفات JSON مرقمة.  
7- التطبيق يجب أن يحتوي على Debugger صغير:  
   - يعرض الكود المستخرج من الموقع.  
   - يوضح العناصر المطابقة للـ Query (Highlight).  

المكتبات المقترحة:  
- requests + BeautifulSoup4 + lxml لاستخراج البيانات.  
- regex للبحث المرن.  
- Flask كخادم API.  
- React أو HTML لواجهة المستخدم.  

اجعل التطبيق شبيه بـ AgentQL Debugger بحيث يمكن كتابة Query واختبار النتيجة فوراً على واجهة المستخدم، مع إمكانية تعديل الاستعلام ورؤية التغيير مباشرة.  
