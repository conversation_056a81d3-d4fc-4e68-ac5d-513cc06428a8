<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات التخطيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-layout"></i> اختبار تحسينات التخطيط</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- وضع الاستخراج -->
                        <div class="form-section">
                            <h3><i class="fas fa-cogs"></i> وضع الاستخراج</h3>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="scraping-mode-selector">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="scrapingMode" id="singlePageMode" value="single">
                                                <label class="form-check-label" for="singlePageMode">
                                                    <i class="fas fa-file"></i> صفحة منفردة
                                                </label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="scrapingMode" id="multiPageMode" value="multi" checked>
                                                <label class="form-check-label" for="multiPageMode">
                                                    <i class="fas fa-files"></i> صفحات متتالية
                                                </label>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">
                                            اختر وضع الاستخراج: صفحة واحدة أو عدة صفحات متتالية
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الصفحات المتتالية - مباشرة بعد وضع الاستخراج -->
                        <div class="form-section" id="multiPageSection">
                            <div class="alert alert-success">
                                <h4><i class="fas fa-check-circle"></i> التحسين الأول: موقع إعدادات الصفحات المتتالية</h4>
                                <p>✅ الآن إعدادات الصفحات المتتالية تظهر مباشرة أسفل وضع الاستخراج</p>
                            </div>
                            
                            <h3><i class="fas fa-file-alt"></i> إعدادات الصفحات المتتالية</h3>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>كيفية الاستخدام:</strong> أدخل 3 صفحات متتالية لاستنتاج النمط تلقائياً، ثم حدد عدد الصفحات المطلوب استخراجها.
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="page1">الصفحة الأولى: <span class="text-danger">*</span></label>
                                        <input type="url" id="page1" class="form-control" placeholder="https://example.com/page/1">
                                        <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="page2">الصفحة الثانية: <span class="text-danger">*</span></label>
                                        <input type="url" id="page2" class="form-control" placeholder="https://example.com/page/2">
                                        <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="page3">الصفحة الثالثة: <span class="text-danger">*</span></label>
                                        <input type="url" id="page3" class="form-control" placeholder="https://example.com/page/3">
                                        <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="maxPages">عدد الصفحات المستهدفة:</label>
                                        <input type="number" id="maxPages" class="form-control" value="5" min="1" max="100">
                                        <small class="form-text text-muted">العدد الإجمالي للصفحات المراد استخراجها</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="startFromPage">البدء من الصفحة:</label>
                                        <input type="number" id="startFromPage" class="form-control" value="1" min="1">
                                        <small class="form-text text-muted">رقم الصفحة للبدء منها</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم الصفحة المنفردة -->
                        <div class="form-section" id="singlePageSection" style="display: none;">
                            <h3><i class="fas fa-link"></i> رابط موقع الأفلام</h3>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="url">رابط الموقع المطلوب استخراج الأفلام منه:</label>
                                        <input type="url" id="url" class="form-control" placeholder="https://movies-site.com/movies">
                                        <small class="form-text text-muted">
                                            أدخل رابط الصفحة التي تحتوي على قائمة الأفلام
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="text-center mb-4">
                            <button id="testScrollBtn" class="btn btn-primary btn-lg">
                                <i class="fas fa-play"></i> اختبار بدء الاستخراج والانتقال التلقائي
                            </button>
                            <button id="toggleModeBtn" class="btn btn-secondary btn-lg">
                                <i class="fas fa-exchange-alt"></i> تبديل الوضع
                            </button>
                        </div>

                        <!-- سجل التطور اللحظي -->
                        <div class="form-section" id="progressLogSection">
                            <div class="alert alert-warning">
                                <h4><i class="fas fa-check-circle"></i> التحسين الثاني: الانتقال التلقائي</h4>
                                <p>✅ عند الضغط على "بدء الاستخراج" ستنتقل الصفحة تلقائياً لهذا القسم</p>
                            </div>
                            
                            <h3><i class="fas fa-list-alt"></i> سجل التطور اللحظي</h3>

                            <!-- إحصائيات التقدم -->
                            <div class="progress-stats" id="progressStats" style="display: block;">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-value">0</div>
                                        <div class="stat-label">العناصر المستخرجة</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">0</div>
                                        <div class="stat-label">الصفحات المعالجة</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">0%</div>
                                        <div class="stat-label">نسبة الإنجاز</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">جاهز للبدء</div>
                                        <div class="stat-label">الحالة</div>
                                    </div>
                                </div>
                            </div>

                            <div class="progress-log-container">
                                <div id="progressLog" class="progress-log">
                                    <div class="log-entry info">
                                        <span class="timestamp">[جاهز]</span>
                                        <span class="message">🚀 التطبيق جاهز للاستخدام</span>
                                    </div>
                                    <div class="log-entry success">
                                        <span class="timestamp">[اختبار]</span>
                                        <span class="message">✅ تم تحسين التخطيط بنجاح</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نتائج الاختبار -->
                        <div class="form-section">
                            <h3><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h3>
                            <div id="testResults" class="alert alert-info">
                                <i class="fas fa-info-circle"></i> اضغط على الأزرار أعلاه لاختبار التحسينات
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة التبديل بين الأوضاع
        function toggleScrapingMode() {
            const singlePageMode = document.getElementById('singlePageMode').checked;
            const multiPageMode = document.getElementById('multiPageMode').checked;
            const singlePageSection = document.getElementById('singlePageSection');
            const multiPageSection = document.getElementById('multiPageSection');

            if (singlePageMode) {
                singlePageSection.style.display = 'block';
                multiPageSection.style.display = 'none';
                updateTestResults('تم التبديل إلى وضع الصفحة المنفردة', 'warning');
            } else if (multiPageMode) {
                singlePageSection.style.display = 'none';
                multiPageSection.style.display = 'block';
                updateTestResults('تم التبديل إلى وضع الصفحات المتتالية - الإعدادات تظهر مباشرة أسفل وضع الاستخراج ✅', 'success');
            }
        }

        // دالة الانتقال التلقائي لسجل التطور
        function scrollToProgressLog() {
            const progressLogSection = document.getElementById('progressLogSection');
            
            if (progressLogSection) {
                progressLogSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
                
                // تأثير بصري
                progressLogSection.style.transition = 'all 0.3s ease';
                progressLogSection.style.backgroundColor = '#e3f2fd';
                progressLogSection.style.border = '2px solid #2196f3';
                progressLogSection.style.borderRadius = '8px';
                
                setTimeout(() => {
                    progressLogSection.style.backgroundColor = '';
                    progressLogSection.style.border = '';
                    progressLogSection.style.borderRadius = '';
                }, 3000);
                
                updateTestResults('✅ تم الانتقال التلقائي لسجل التطور اللحظي بنجاح!', 'success');
                
                // إضافة رسالة في السجل
                addLogMessage('📍 تم الانتقال تلقائياً لسجل التطور اللحظي');
            }
        }

        // إضافة رسالة للسجل
        function addLogMessage(message) {
            const progressLog = document.getElementById('progressLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry info';
            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                <span class="message">${message}</span>
            `;
            progressLog.appendChild(logEntry);
            progressLog.scrollTop = progressLog.scrollHeight;
        }

        // تحديث نتائج الاختبار
        function updateTestResults(message, type) {
            const testResults = document.getElementById('testResults');
            testResults.className = `alert alert-${type}`;
            testResults.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
        }

        // تبديل الوضع للاختبار
        function toggleModeForTest() {
            const singlePageMode = document.getElementById('singlePageMode');
            const multiPageMode = document.getElementById('multiPageMode');
            
            if (multiPageMode.checked) {
                singlePageMode.checked = true;
                multiPageMode.checked = false;
            } else {
                singlePageMode.checked = false;
                multiPageMode.checked = true;
            }
            
            toggleScrapingMode();
        }

        // ربط الأحداث
        document.getElementById('singlePageMode').addEventListener('change', toggleScrapingMode);
        document.getElementById('multiPageMode').addEventListener('change', toggleScrapingMode);
        document.getElementById('testScrollBtn').addEventListener('click', scrollToProgressLog);
        document.getElementById('toggleModeBtn').addEventListener('click', toggleModeForTest);

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            toggleScrapingMode();
            updateTestResults('✅ تم تحميل صفحة اختبار التحسينات - جاهز للاختبار', 'info');
            console.log('✅ تم تحميل صفحة اختبار تحسينات التخطيط');
        });
    </script>
</body>
</html>
