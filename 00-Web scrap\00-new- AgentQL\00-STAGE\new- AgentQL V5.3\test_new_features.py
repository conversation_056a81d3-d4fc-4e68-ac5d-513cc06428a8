#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الميزات الجديدة للتطبيق
- أزرار التحكم في العمليات (إيقاف/استئناف/إيقاف مع تصدير)
- نظام تصفية وتحليل النتائج
"""

import requests
import json
import time

# إعدادات الاختبار
BASE_URL = "http://localhost:5000"
TEST_DATA = [
    {"title": "فيلم الأكشن الرائع", "link": "http://example.com/action1", "image": "http://example.com/img1.jpg", "type": "movie"},
    {"title": "مسلسل الدراما الجميل", "link": "http://example.com/drama1", "image": "http://example.com/img2.jpg", "type": "series"},
    {"title": "فيلم الكوميديا المضحك", "link": "http://example.com/comedy1", "image": "http://example.com/img3.jpg", "type": "movie"},
    {"title": "مسلسل الأكشن المثير", "link": "http://example.com/action2", "image": "http://example.com/img4.jpg", "type": "series"},
    {"title": "فيلم الرعب المخيف", "link": "http://example.com/horror1", "image": "http://example.com/img5.jpg", "type": "movie"},
    {"title": "مسلسل الكوميديا الرائع", "link": "http://example.com/comedy2", "image": "http://example.com/img6.jpg", "type": "series"},
]

def test_control_scraping():
    """اختبار وظائف التحكم في العمليات"""
    print("🧪 اختبار وظائف التحكم في العمليات...")
    
    # اختبار إيقاف مؤقت
    print("\n1. اختبار الإيقاف المؤقت:")
    response = requests.post(f"{BASE_URL}/api/control_scraping", 
                           json={"action": "pause"})
    print(f"   الاستجابة: {response.json()}")
    
    # اختبار الاستئناف
    print("\n2. اختبار الاستئناف:")
    response = requests.post(f"{BASE_URL}/api/control_scraping", 
                           json={"action": "resume"})
    print(f"   الاستجابة: {response.json()}")
    
    # اختبار الإيقاف مع التصدير
    print("\n3. اختبار الإيقاف مع التصدير:")
    response = requests.post(f"{BASE_URL}/api/control_scraping", 
                           json={"action": "stop_and_export"})
    print(f"   الاستجابة: {response.json()}")

def test_filter_results():
    """اختبار وظائف تصفية النتائج"""
    print("\n🧪 اختبار وظائف تصفية النتائج...")
    
    # اختبار استبعاد الأفلام المحتوية على كلمة "أكشن"
    print("\n1. اختبار استبعاد الأفلام المحتوية على 'أكشن':")
    response = requests.post(f"{BASE_URL}/api/filter_results", 
                           json={
                               "results": TEST_DATA,
                               "filter_type": "exclude",
                               "search_term": "أكشن"
                           })
    result = response.json()
    print(f"   النتيجة: {result['message']}")
    print(f"   عدد النتائج المصفاة: {result['total_filtered']}")
    print(f"   عدد المستبعدة: {result['total_excluded']}")
    print(f"   عدد المختارة: {result['total_included']}")
    
    # اختبار اختيار الأفلام المحتوية على كلمة "كوميديا"
    print("\n2. اختبار اختيار الأفلام المحتوية على 'كوميديا':")
    response = requests.post(f"{BASE_URL}/api/filter_results", 
                           json={
                               "results": TEST_DATA,
                               "filter_type": "include",
                               "search_term": "كوميديا"
                           })
    result = response.json()
    print(f"   النتيجة: {result['message']}")
    print(f"   عدد النتائج المصفاة: {result['total_filtered']}")
    print(f"   عدد المستبعدة: {result['total_excluded']}")
    print(f"   عدد المختارة: {result['total_included']}")
    
    # اختبار عرض المستبعدة
    print("\n3. اختبار عرض المستبعدة:")
    response = requests.post(f"{BASE_URL}/api/filter_results", 
                           json={
                               "results": TEST_DATA,
                               "filter_type": "show_excluded",
                               "search_term": "مسلسل"
                           })
    result = response.json()
    print(f"   النتيجة: {result['message']}")
    print(f"   عدد النتائج المصفاة: {result['total_filtered']}")
    
    # اختبار عرض المختارة
    print("\n4. اختبار عرض المختارة:")
    response = requests.post(f"{BASE_URL}/api/filter_results", 
                           json={
                               "results": TEST_DATA,
                               "filter_type": "show_included",
                               "search_term": "فيلم"
                           })
    result = response.json()
    print(f"   النتيجة: {result['message']}")
    print(f"   عدد النتائج المصفاة: {result['total_filtered']}")

def test_export_filtered():
    """اختبار تصدير النتائج المصفاة"""
    print("\n🧪 اختبار تصدير النتائج المصفاة...")
    
    # أولاً، تصفية البيانات
    response = requests.post(f"{BASE_URL}/api/filter_results", 
                           json={
                               "results": TEST_DATA,
                               "filter_type": "include",
                               "search_term": "فيلم"
                           })
    filtered_data = response.json()['filtered_results']
    
    # ثم تصدير النتائج المصفاة
    response = requests.post(f"{BASE_URL}/api/export", 
                           json={
                               "results": filtered_data,
                               "export_type": "single",
                               "export_format": "json"
                           })
    result = response.json()
    print(f"   نتيجة التصدير: {result['message']}")
    print(f"   عدد الملفات: {result['total_files']}")

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار الميزات الجديدة للتطبيق")
    print("=" * 50)
    
    try:
        # اختبار وظائف التحكم
        test_control_scraping()
        
        # اختبار وظائف التصفية
        test_filter_results()
        
        # اختبار التصدير المصفى
        test_export_filtered()
        
        print("\n✅ تم إنجاز جميع الاختبارات بنجاح!")
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على http://localhost:5000")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    run_all_tests()
