import requests
from bs4 import BeautifulSoup
import json
import re
import os
from urllib.parse import urljoin, urlparse
import time
from datetime import datetime


class QueryParser:
    """كلاس لتحليل الاستعلامات البسيطة وتحويلها إلى BeautifulSoup selectors"""
    
    def __init__(self):
        self.query_pattern = re.compile(
            r'SELECT\s+(.+?)\s+FROM\s+(\w+)(?:\s+WHERE\s+(.+?))?$',
            re.IGNORECASE | re.DOTALL
        )
    
    def parse_query(self, query):
        """تحليل الاستعلام وإرجاع معلومات التحليل"""
        query = query.strip()

        # إذا كان الاستعلام بصيغة SQL
        match = self.query_pattern.match(query)
        if match:
            select_part = match.group(1).strip()
            from_part = match.group(2).strip()
            where_part = match.group(3).strip() if match.group(3) else None

            # تحليل الحقول المطلوبة
            fields = [field.strip() for field in select_part.split(',')]

            # تحليل شروط WHERE
            selector = self._build_selector(where_part) if where_part else None

            return {
                'fields': fields,
                'source': from_part,
                'selector': selector,
                'where_conditions': where_part
            }

        # إذا كان الاستعلام بسيط (كلمة واحدة أو أكثر)
        else:
            # استعلام بسيط - البحث عن أفلام أو مسلسلات
            simple_keywords = query.lower().split()

            # تحديد نوع المحتوى المطلوب
            if any(keyword in ['movies', 'films', 'أفلام', 'فيلم'] for keyword in simple_keywords):
                source = 'movies'
            elif any(keyword in ['series', 'shows', 'مسلسلات', 'مسلسل'] for keyword in simple_keywords):
                source = 'series'
            else:
                source = 'all'  # البحث عن كل شيء

            return {
                'fields': ['title', 'link', 'image'],  # الحقول الافتراضية
                'source': source,
                'selector': None,  # لا يوجد selector محدد
                'where_conditions': None,
                'simple_query': True
            }
    
    def _build_selector(self, where_conditions):
        """بناء CSS selector من شروط WHERE"""
        if not where_conditions:
            return None
        
        # تحليل الشروط البسيطة
        conditions = []
        
        # البحث عن شروط class
        class_matches = re.findall(r'class\s*=\s*["\']([^"\']+)["\']', where_conditions, re.IGNORECASE)
        for class_name in class_matches:
            conditions.append(f'.{class_name}')
        
        # البحث عن شروط id
        id_matches = re.findall(r'id\s*=\s*["\']([^"\']+)["\']', where_conditions, re.IGNORECASE)
        for id_name in id_matches:
            conditions.append(f'#{id_name}')
        
        # البحث عن شروط tag
        tag_matches = re.findall(r'tag\s*=\s*["\']([^"\']+)["\']', where_conditions, re.IGNORECASE)
        for tag_name in tag_matches:
            conditions.append(tag_name)
        
        return ' '.join(conditions) if conditions else None


class ScrapingStrategy:
    """كلاس أساسي لاستراتيجيات الاستخراج"""

    def __init__(self, name, description):
        self.name = name
        self.description = description
        self.success_rate = 0.0
        self.items_found = 0

    def extract_data(self, soup, base_url):
        """استخراج البيانات - يجب تنفيذها في الكلاسات الفرعية"""
        raise NotImplementedError

    def evaluate_success(self, results):
        """تقييم نجاح الاستراتيجية"""
        self.items_found = len(results)
        if results:
            # تقييم جودة البيانات
            quality_score = 0
            for item in results:
                if item.get('title'):
                    quality_score += 3
                if item.get('link'):
                    quality_score += 2
                if item.get('image'):
                    quality_score += 1

            self.success_rate = min(quality_score / (len(results) * 6), 1.0)
        else:
            self.success_rate = 0.0

        return self.success_rate


class MovieCardStrategy(ScrapingStrategy):
    """استراتيجية بطاقات الأفلام التقليدية"""

    def __init__(self):
        super().__init__("Movie Cards", "البحث عن بطاقات الأفلام التقليدية")

    def extract_data(self, soup, base_url):
        results = []

        # قائمة شاملة من selectors لبطاقات الأفلام - محسنة للمواقع المستهدفة
        movie_card_selectors = [
            # الأنماط العامة
            '.movie-card', '.movie-item', '.film-card', '.film-item',
            '.movie-box', '.film-box', '.movie-poster', '.film-poster',
            '.movie', '.film', '.card', '.item', '.post', '.entry',
            'a[class*="movie"]', 'a[class*="film"]', 'a[class*="card"]',
            '.grid-item', '.list-item', '.content-item', '.media-item',

            # أنماط محددة للمواقع المستهدفة
            '.post__image',  # للموقع a.asd.homes
            '.pm-video-thumb',  # للمواقع mc.turkishasq.com و vid.shahidwbas.tv
            '.thumbnail',  # نمط Bootstrap
            'a img',  # روابط تحتوي على صور
            'div.thumbnail',  # div مع class thumbnail
            '.pm-video-labels',  # عناصر الفيديو
            '.caption'  # العناوين التوضيحية
        ]

        for selector in movie_card_selectors:
            try:
                elements = soup.select(selector)
                if elements and len(elements) > 2:  # على الأقل 3 عناصر
                    for element in elements:
                        item = self._extract_movie_data(element, base_url)
                        if item and (item.get('title') or item.get('link')):
                            results.append(item)

                    if results:
                        print(f"✅ استراتيجية بطاقات الأفلام: وُجدت {len(results)} عنصر باستخدام {selector}")
                        break
            except Exception:
                continue

        return results

    def _extract_movie_data(self, element, base_url):
        """استخراج بيانات فيلم من عنصر HTML"""
        from urllib.parse import urljoin

        item = {}

        # استخراج العنوان
        title = self._find_title_comprehensive(element)
        if title:
            item['title'] = title

        # استخراج الرابط
        link = self._find_link_comprehensive(element, base_url)
        if link:
            item['link'] = link

        # استخراج الصورة
        image = self._find_image_comprehensive(element, base_url)
        if image:
            item['image'] = image

        # تحديد النوع
        content_type = self._detect_content_type_comprehensive(element, title)
        item['type'] = content_type

        return item

    def _find_title_comprehensive(self, element):
        """البحث الشامل عن العنوان"""
        title_sources = []

        # 1. البحث في attributes
        for attr in ['title', 'alt', 'data-title', 'data-name', 'aria-label']:
            if element.get(attr):
                title_sources.append(element.get(attr))

        # 2. البحث في الصور الفرعية
        img = element.find('img')
        if img:
            for attr in ['alt', 'title', 'data-title']:
                if img.get(attr):
                    title_sources.append(img.get(attr))

        # 3. البحث في العناصر النصية الفرعية
        text_selectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            '.title', '.name', '.movie-title', '.film-title',
            '.movie-name', '.film-name', '.post-title',
            'span[class*="title"]', 'div[class*="title"]',
            'p[class*="title"]', 'a[class*="title"]'
        ]

        for selector in text_selectors:
            try:
                sub_element = element.select_one(selector)
                if sub_element:
                    text = sub_element.get_text(strip=True)
                    if text and len(text) < 200:
                        title_sources.append(text)
            except:
                continue

        # 4. البحث في النص المباشر (كحل أخير)
        direct_text = element.get_text(strip=True)
        if direct_text and len(direct_text) < 100:
            clean_text = ' '.join(direct_text.split())
            if clean_text:
                title_sources.append(clean_text)

        # إرجاع أول عنوان صالح
        for title in title_sources:
            if title and len(title.strip()) > 2:
                clean_title = title.strip()
                unwanted = ['click', 'more', 'read', 'view', 'watch', 'download']
                if not any(word in clean_title.lower() for word in unwanted):
                    return clean_title

        return None

    def _find_link_comprehensive(self, element, base_url):
        """البحث الشامل عن الرابط مع تحسينات لتجنب الروابط الخاطئة"""
        from urllib.parse import urljoin

        # قائمة الروابط المستبعدة
        excluded_patterns = [
            '#modal-login-form',
            'categories.php',
            'page=',
            'order=',
            'javascript:',
            'mailto:',
            '#',
            'login',
            'register',
            'signup'
        ]

        # كلمات مفتاحية للروابط المفضلة
        preferred_keywords = ['watch', 'view', 'play', 'movie', 'film', 'video', 'vid=']

        def is_valid_link(href):
            """فحص صحة الرابط"""
            if not href or href == '#':
                return False

            href_lower = href.lower()

            # استبعاد الروابط غير المرغوبة
            for pattern in excluded_patterns:
                if pattern in href_lower:
                    return False

            return True

        def get_link_priority(href):
            """حساب أولوية الرابط"""
            if not href:
                return 0

            href_lower = href.lower()
            priority = 1

            # أولوية عالية جداً لموقع shahidwbas.tv مع watch.php
            if 'shahidwbas.tv' in href_lower and 'watch.php' in href_lower:
                priority += 20

            # أولوية عالية للروابط التي تحتوي على كلمات مفتاحية
            for keyword in preferred_keywords:
                if keyword in href_lower:
                    priority += 10
                    break

            # أولوية إضافية للروابط التي تحتوي على معرف الفيديو
            if 'vid=' in href_lower or 'id=' in href_lower:
                priority += 5

            return priority

        # 1. إذا كان العنصر نفسه رابط
        if element.name == 'a' and element.get('href'):
            href = element.get('href')
            if is_valid_link(href):
                return urljoin(base_url, href)

        # 2. جمع جميع الروابط الفرعية وترتيبها حسب الأولوية
        all_links = element.find_all('a', href=True)
        valid_links = []

        for link in all_links:
            href = link.get('href')
            if is_valid_link(href):
                priority = get_link_priority(href)
                valid_links.append((href, priority))

        # ترتيب الروابط حسب الأولوية (الأعلى أولاً)
        valid_links.sort(key=lambda x: x[1], reverse=True)

        # 3. إرجاع أفضل رابط
        if valid_links:
            best_href = valid_links[0][0]
            return urljoin(base_url, best_href)

        return None

    def _find_image_comprehensive(self, element, base_url):
        """البحث الشامل عن الصورة"""
        from urllib.parse import urljoin

        # 1. إذا كان العنصر نفسه صورة
        if element.name == 'img':
            src = element.get('src') or element.get('data-src') or element.get('data-original')
            if src:
                return urljoin(base_url, src)

        # 2. البحث عن صورة في العناصر الفرعية
        img_elements = element.find_all('img')
        for img in img_elements:
            src = img.get('src')
            if src and not src.startswith('data:') and 'placeholder' not in src.lower():
                return urljoin(base_url, src)

            for attr in ['data-src', 'data-original', 'data-lazy', 'data-image']:
                src = img.get(attr)
                if src and not src.startswith('data:'):
                    return urljoin(base_url, src)

        return None

    def _detect_content_type_comprehensive(self, element, title):
        """تحديد نوع المحتوى (فيلم أم مسلسل)"""
        if not title:
            return 'movie'

        title_lower = title.lower()
        series_keywords = ['series', 'season', 'episode', 'مسلسل', 'موسم', 'حلقة', 'مسلسلات']

        if any(keyword in title_lower for keyword in series_keywords):
            return 'series'

        return 'movie'

    def _find_title(self, element):
        """البحث عن العنوان"""
        # البحث في attributes
        for attr in ['title', 'alt', 'data-title', 'data-name', 'aria-label']:
            if element.get(attr):
                return element.get(attr).strip()

        # البحث في الصور
        img = element.find('img')
        if img:
            for attr in ['alt', 'title', 'data-title']:
                if img.get(attr):
                    return img.get(attr).strip()

        # البحث في العناصر النصية
        text_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '.movie-title']
        for selector in text_selectors:
            try:
                sub_element = element.select_one(selector)
                if sub_element:
                    text = sub_element.get_text(strip=True)
                    if text and len(text) < 200:
                        return text
            except:
                continue

        return None

    def _find_link(self, element, base_url):
        """البحث عن الرابط مع تحسينات لتجنب الروابط الخاطئة"""

        # قائمة الروابط المستبعدة
        excluded_patterns = [
            '#modal-login-form',
            'categories.php',
            'page=',
            'order=',
            'javascript:',
            'mailto:',
            '#',
            'login',
            'register',
            'signup'
        ]

        # كلمات مفتاحية للروابط المفضلة
        preferred_keywords = ['watch', 'view', 'play', 'movie', 'film', 'video', 'vid=']

        def is_valid_link(href):
            """فحص صحة الرابط"""
            if not href or href == '#':
                return False

            href_lower = href.lower()

            # استبعاد الروابط غير المرغوبة
            for pattern in excluded_patterns:
                if pattern in href_lower:
                    return False

            return True

        def get_link_priority(href):
            """حساب أولوية الرابط"""
            if not href:
                return 0

            href_lower = href.lower()
            priority = 1

            # أولوية عالية جداً لموقع shahidwbas.tv مع watch.php
            if 'shahidwbas.tv' in href_lower and 'watch.php' in href_lower:
                priority += 20

            # أولوية عالية للروابط التي تحتوي على كلمات مفتاحية
            for keyword in preferred_keywords:
                if keyword in href_lower:
                    priority += 10
                    break

            # أولوية إضافية للروابط التي تحتوي على معرف الفيديو
            if 'vid=' in href_lower or 'id=' in href_lower:
                priority += 5

            return priority

        # 1. إذا كان العنصر نفسه رابط
        if element.name == 'a' and element.get('href'):
            href = element.get('href')
            if is_valid_link(href):
                return urljoin(base_url, href)

        # 2. جمع جميع الروابط الفرعية وترتيبها حسب الأولوية
        all_links = element.find_all('a', href=True)
        valid_links = []

        for link in all_links:
            href = link.get('href')
            if is_valid_link(href):
                priority = get_link_priority(href)
                valid_links.append((href, priority))

        # ترتيب الروابط حسب الأولوية (الأعلى أولاً)
        valid_links.sort(key=lambda x: x[1], reverse=True)

        # 3. إرجاع أفضل رابط
        if valid_links:
            best_href = valid_links[0][0]
            return urljoin(base_url, best_href)

        return None

    def _find_image(self, element, base_url):
        """البحث عن الصورة"""
        if element.name == 'img':
            src = element.get('src') or element.get('data-src')
            if src:
                return urljoin(base_url, src)

        img = element.find('img')
        if img:
            src = img.get('src') or img.get('data-src') or img.get('data-original')
            if src and not src.startswith('data:'):
                return urljoin(base_url, src)

        return None

    def _detect_content_type(self, element, title):
        """تحديد نوع المحتوى"""
        text = element.get_text().lower()
        if title:
            text += ' ' + title.lower()

        series_keywords = ['مسلسل', 'series', 'tv show', 'season', 'episode']
        movie_keywords = ['فيلم', 'movie', 'film', 'cinema']

        series_score = sum(1 for keyword in series_keywords if keyword in text)
        movie_score = sum(1 for keyword in movie_keywords if keyword in text)

        return 'series' if series_score > movie_score else 'movie'


class ListBasedStrategy(ScrapingStrategy):
    """استراتيجية القوائم والجداول"""

    def __init__(self):
        super().__init__("List Based", "البحث في القوائم والجداول")

    def extract_data(self, soup, base_url):
        results = []

        # البحث في الجداول
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')[1:]  # تجاهل الرأس
            if len(rows) > 2:
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        item = self._extract_from_row(cells, base_url)
                        if item:
                            results.append(item)

        # البحث في القوائم
        lists = soup.find_all(['ul', 'ol'])
        for list_elem in lists:
            items = list_elem.find_all('li')
            if len(items) > 3:
                for li in items:
                    item = self._extract_from_list_item(li, base_url)
                    if item:
                        results.append(item)

        return results

    def _extract_from_row(self, cells, base_url):
        """استخراج البيانات من صف جدول"""
        item = {}

        for cell in cells:
            text = cell.get_text(strip=True)
            link = cell.find('a')
            img = cell.find('img')

            if not item.get('title') and text and len(text) > 3:
                item['title'] = text

            if not item.get('link') and link and link.get('href'):
                item['link'] = urljoin(base_url, link.get('href'))

            if not item.get('image') and img and img.get('src'):
                item['image'] = urljoin(base_url, img.get('src'))

        if item.get('title') or item.get('link'):
            item['type'] = 'movie'  # افتراضي
            return item

        return None

    def _extract_from_list_item(self, li, base_url):
        """استخراج البيانات من عنصر قائمة"""
        item = {}

        text = li.get_text(strip=True)
        link = li.find('a')
        img = li.find('img')

        if text and len(text) > 3:
            item['title'] = text

        if link and link.get('href'):
            item['link'] = urljoin(base_url, link.get('href'))

        if img and img.get('src'):
            item['image'] = urljoin(base_url, img.get('src'))

        if item.get('title') or item.get('link'):
            # تحديد نوع المحتوى
            title = item.get('title', '')
            title_lower = title.lower()
            series_keywords = ['series', 'season', 'episode', 'مسلسل', 'موسم', 'حلقة', 'مسلسلات']

            if any(keyword in title_lower for keyword in series_keywords):
                item['type'] = 'series'
            else:
                item['type'] = 'movie'

            return item

        return None


class AflamTopStrategy(ScrapingStrategy):
    """استراتيجية مخصصة لمواقع مثل aflam.top وما شابهها"""

    def __init__(self):
        super().__init__("Aflam Top", "استراتيجية مخصصة لمواقع aflam.top وما شابهها")

    def extract_data(self, soup, base_url):
        results = []
        processed_links = set()  # لتجنب التكرار

        # قائمة شاملة من selectors مخصصة لمواقع aflam.top
        aflam_selectors = [
            # أنماط محددة لمواقع aflam.top (أولوية عالية)
            'article.post', 'div.post', '.movie-post', '.film-post',
            '.post-item', '.movie-item', '.film-item',

            # أنماط الشبكة والبطاقات
            '.grid-item', '.card-item', '.movie-card', '.film-card',
            '.content-item', '.media-item', '.video-item',

            # أنماط Bootstrap المحتملة
            '.col-md-3', '.col-lg-3', '.col-sm-4', '.col-xs-6',
            '.col-md-4', '.col-lg-4', '.col-sm-6', '.col-xs-12',

            # أنماط عامة للمحتوى
            'article', '.entry', '.item', '.box',
            '.content-box', '.media-box', '.video-box',
            '.thumbnail-container', '.poster-container',

            # أنماط ديناميكية
            'div[class*="post"]', 'div[class*="movie"]', 'div[class*="film"]',
            'div[class*="item"]', 'div[class*="card"]', 'div[class*="content"]',

            # أنماط أخرى محتملة
            '.grid-col', '.flex-item', '.list-item',
            'a[href*="movie"]', 'a[href*="film"]', 'a[href*="watch"]'
        ]

        print(f"🔍 بدء البحث في موقع aflam.top...")

        for selector in aflam_selectors:
            try:
                elements = soup.select(selector)
                if elements and len(elements) > 2:  # على الأقل 3 عناصر
                    temp_results = []
                    print(f"🔍 جاري فحص {len(elements)} عنصر باستخدام: {selector}")

                    for element in elements:
                        item = self._extract_aflam_data(element, base_url)
                        if item and self._is_valid_movie_item(item):
                            # تجنب التكرار
                            if item.get('link') not in processed_links:
                                temp_results.append(item)
                                processed_links.add(item.get('link'))

                    # إذا وجدنا نتائج جيدة، نستخدمها
                    if len(temp_results) > len(results):
                        results = temp_results
                        print(f"✅ استراتيجية aflam.top: وُجدت {len(results)} عنصر صالح باستخدام {selector}")

                        # طباعة عينة من النتائج للتتبع
                        for i, item in enumerate(results[:3]):
                            print(f"   📋 {i+1}. {item.get('title', 'بدون عنوان')[:50]}")
                            if item.get('image') and item.get('image') != 'غير متوفر':
                                print(f"      🖼️ صورة: {item.get('image')[:60]}...")

            except Exception as e:
                print(f"❌ خطأ في selector {selector}: {str(e)}")
                continue

        # إذا لم نجد نتائج، جرب البحث في الروابط المباشرة
        if not results:
            print("🔍 لم توجد نتائج، جاري البحث في الروابط المباشرة...")
            results = self._extract_direct_links(soup, base_url)

        print(f"📊 إجمالي النتائج النهائية: {len(results)}")
        return results

    def _extract_aflam_data(self, element, base_url):
        """استخراج بيانات فيلم من عنصر HTML مخصص لمواقع aflam.top"""
        from urllib.parse import urljoin

        item = {}

        # استخراج العنوان بطرق متعددة
        title = self._find_aflam_title(element)
        if title:
            item['title'] = title

        # استخراج الرابط
        link = self._find_aflam_link(element, base_url)
        if link:
            item['link'] = link

        # استخراج الصورة
        image = self._find_aflam_image(element, base_url)
        if image:
            item['image'] = image

        # تحديد النوع
        content_type = self._detect_aflam_content_type(element, title)
        item['type'] = content_type

        return item

    def _find_aflam_title(self, element):
        """البحث عن العنوان في مواقع aflam.top"""
        title_sources = []

        # 1. البحث في attributes مخصصة
        for attr in ['title', 'alt', 'data-title', 'data-name', 'aria-label', 'data-original-title']:
            if element.get(attr):
                title_sources.append(element.get(attr))

        # 2. البحث في الصور
        img = element.find('img')
        if img:
            for attr in ['alt', 'title', 'data-title', 'data-original-title']:
                if img.get(attr):
                    title_sources.append(img.get(attr))

        # 3. البحث في العناصر النصية
        text_selectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            '.title', '.name', '.movie-title', '.film-title',
            '.post-title', '.entry-title', '.card-title',
            'span[class*="title"]', 'div[class*="title"]',
            'p[class*="title"]', 'a[class*="title"]',
            '.movie-name', '.film-name'
        ]

        for selector in text_selectors:
            try:
                sub_element = element.select_one(selector)
                if sub_element:
                    text = sub_element.get_text(strip=True)
                    if text and len(text) < 200:
                        title_sources.append(text)
            except:
                continue

        # 4. البحث في الروابط
        link = element.find('a')
        if link:
            if link.get('title'):
                title_sources.append(link.get('title'))
            # استخراج العنوان من النص داخل الرابط
            link_text = link.get_text(strip=True)
            if link_text and len(link_text) < 100:
                title_sources.append(link_text)

        # إرجاع أول عنوان صالح
        for title in title_sources:
            if title and len(title.strip()) > 2:
                clean_title = title.strip()
                # تنظيف العنوان من الكلمات غير المرغوبة
                unwanted = ['click', 'more', 'read', 'view', 'watch', 'download', 'مشاهدة', 'تحميل']
                if not any(word in clean_title.lower() for word in unwanted):
                    return clean_title

        return None

    def _find_aflam_link(self, element, base_url):
        """البحث عن الرابط في مواقع aflam.top"""
        from urllib.parse import urljoin

        # البحث في الروابط المباشرة
        if element.name == 'a' and element.get('href'):
            href = element.get('href')
            if self._is_valid_aflam_link(href):
                return urljoin(base_url, href)

        # البحث في الروابط الفرعية
        links = element.find_all('a', href=True)
        for link in links:
            href = link.get('href')
            if self._is_valid_aflam_link(href):
                return urljoin(base_url, href)

        return None

    def _find_aflam_image(self, element, base_url):
        """البحث عن الصورة في مواقع aflam.top مع معالجة خاصة للصور الافتراضية"""
        from urllib.parse import urljoin

        # قائمة الصور الافتراضية التي يجب تجاهلها
        default_images = [
            'default.jpg', 'default.png', 'placeholder.jpg', 'placeholder.png',
            'no-image.jpg', 'no-image.png', 'loading.gif', 'loader.gif',
            'blank.jpg', 'blank.png', 'empty.jpg', 'empty.png',
            'logo.png', 'logo.jpg', 'favicon.ico', 'icon.png'
        ]

        def is_valid_image(src):
            """فحص صحة رابط الصورة"""
            if not src or src.startswith('data:'):
                return False

            src_lower = src.lower()

            # تجاهل الصور الافتراضية
            for default_img in default_images:
                if default_img in src_lower:
                    return False

            # تجاهل الصور الصغيرة جداً (أقل من 50x50)
            if any(size in src_lower for size in ['1x1', '10x10', '20x20', '30x30']):
                return False

            # تفضيل الصور الكبيرة
            return True

        candidate_images = []

        # البحث في الصور المباشرة
        if element.name == 'img':
            # أولوية للصور المؤجلة (lazy loading)
            for attr in ['data-src', 'data-original', 'data-lazy', 'data-image', 'src']:
                src = element.get(attr)
                if src and is_valid_image(src):
                    candidate_images.append((urljoin(base_url, src), attr))

        # البحث في الصور الفرعية
        images = element.find_all('img')
        for img in images:
            # أولوية للصور المؤجلة
            for attr in ['data-src', 'data-original', 'data-lazy', 'data-image', 'src']:
                src = img.get(attr)
                if src and is_valid_image(src):
                    candidate_images.append((urljoin(base_url, src), attr))

        # البحث في background images
        style = element.get('style', '')
        if 'background-image' in style:
            import re
            match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
            if match:
                src = match.group(1)
                if is_valid_image(src):
                    candidate_images.append((urljoin(base_url, src), 'background'))

        # البحث في data attributes للعنصر نفسه
        for attr in ['data-bg', 'data-background', 'data-poster', 'data-thumb']:
            src = element.get(attr)
            if src and is_valid_image(src):
                candidate_images.append((urljoin(base_url, src), attr))

        # ترتيب الصور حسب الأولوية
        if candidate_images:
            # أولوية للصور المؤجلة ثم الصور العادية
            priority_order = ['data-src', 'data-original', 'data-lazy', 'data-image', 'data-poster', 'data-thumb', 'src', 'background']

            for priority_attr in priority_order:
                for img_url, attr in candidate_images:
                    if attr == priority_attr:
                        return img_url

            # إذا لم نجد صورة بالأولوية، إرجاع أول صورة صالحة
            return candidate_images[0][0]

        return None

    def _is_valid_aflam_link(self, href):
        """فحص صحة الرابط لمواقع aflam.top"""
        if not href or href == '#' or href.startswith('javascript:'):
            return False

        # استبعاد الروابط غير المرغوبة
        excluded_patterns = [
            'login', 'register', 'signup', 'contact',
            'about', 'privacy', 'terms', 'category',
            'tag', 'author', 'search', 'page=',
            'order=', 'sort=', '#modal', '.css', '.js',
            'genre/', 'last/', 'javascript:', 'mailto:',
            '#', 'home', 'index', 'main', 'الرئيسية',
            'المضاف', 'حديثا', 'افلام', 'مسلسلات'
        ]

        href_lower = href.lower()
        for pattern in excluded_patterns:
            if pattern in href_lower:
                return False

        return True

    def _detect_aflam_content_type(self, element, title):
        """تحديد نوع المحتوى (فيلم أم مسلسل)"""
        if not title:
            return 'movie'  # افتراضي

        title_lower = title.lower()

        # كلمات مفتاحية للمسلسلات
        series_keywords = [
            'مسلسل', 'series', 'season', 'episode', 'موسم', 'حلقة',
            'مسلسلات', 'برنامج', 'show', 'tv', 'الموسم', 'الحلقة'
        ]

        # كلمات مفتاحية للأفلام
        movie_keywords = [
            'فيلم', 'movie', 'film', 'أفلام', 'cinema', 'فلم'
        ]

        series_score = sum(1 for keyword in series_keywords if keyword in title_lower)
        movie_score = sum(1 for keyword in movie_keywords if keyword in title_lower)

        return 'series' if series_score > movie_score else 'movie'

    def _is_valid_movie_item(self, item):
        """فحص صحة عنصر الفيلم"""
        # يجب أن يحتوي على عنوان أو رابط على الأقل
        if not item.get('title') and not item.get('link'):
            return False

        # فحص جودة العنوان
        title = item.get('title', '')
        if title:
            # استبعاد العناوين القصيرة جداً أو الطويلة جداً
            if len(title) < 2 or len(title) > 200:
                return False

            # استبعاد العناوين التي تحتوي على كلمات غير مرغوبة
            unwanted_titles = [
                'loading', 'error', 'not found', 'click here',
                'more info', 'read more', 'view all',
                'الرئيسية', 'المضاف حديثا', 'افلام الأكشن',
                'افلام الرعب', 'افلام الخيال العلمي', 'افلام الكوميديا',
                'افلام الدراما', 'مسلسلات', 'برامج', 'انمي',
                'home', 'genre', 'category', 'latest', 'recent'
            ]

            title_lower = title.lower()
            if any(unwanted in title_lower for unwanted in unwanted_titles):
                return False

        return True

    def _extract_direct_links(self, soup, base_url):
        """استخراج الروابط المباشرة كحل احتياطي مع معالجة محسنة للصور"""
        from urllib.parse import urljoin

        results = []
        processed_links = set()

        # البحث عن جميع الروابط في الصفحة
        links = soup.find_all('a', href=True)
        print(f"🔍 فحص {len(links)} رابط مباشر...")

        for link in links:
            href = link.get('href')
            if not self._is_valid_aflam_link(href):
                continue

            full_link = urljoin(base_url, href)
            if full_link in processed_links:
                continue

            # استخراج العنوان من الرابط
            title = self._find_aflam_title(link)
            if not title or len(title.strip()) < 3:
                continue

            # البحث عن صورة مرتبطة مع معالجة محسنة
            image = self._find_aflam_image(link, base_url)

            item = {
                'title': title.strip(),
                'link': full_link,
                'image': image or 'غير متوفر',
                'type': self._detect_aflam_content_type(link, title)
            }

            if self._is_valid_movie_item(item):
                results.append(item)
                processed_links.add(full_link)

                # طباعة معلومات للتتبع (أول 5 فقط)
                if len(results) <= 5:
                    print(f"✅ رابط مباشر: {title[:50]}...")
                    if image and image != 'غير متوفر':
                        print(f"   🖼️ صورة: {image[:60]}...")

        print(f"📊 تم استخراج {len(results)} رابط مباشر صالح")
        return results[:50]  # حد أقصى 50 عنصر


class StrategyManager:
    """مدير الاستراتيجيات - للتبديل بين الاستراتيجيات المختلفة"""

    def __init__(self):
        self.strategies = {
            1: {
                'name': 'Movie Cards Strategy',
                'description': 'استراتيجية بطاقات الأفلام التقليدية',
                'class': MovieCardStrategy,
                'enabled': True
            },
            2: {
                'name': 'List Based Strategy',
                'description': 'استراتيجية القوائم والجداول',
                'class': ListBasedStrategy,
                'enabled': True
            },
            3: {
                'name': 'Aflam Top Strategy',
                'description': 'استراتيجية مخصصة لمواقع مثل aflam.top',
                'class': AflamTopStrategy,
                'enabled': True
            }
        }
        self.active_strategies = []
        self.manual_strategy = None
        self.auto_mode = True

    def get_available_strategies(self):
        """الحصول على قائمة الاستراتيجيات المتاحة"""
        return {k: v for k, v in self.strategies.items() if v['enabled'] and v['class']}

    def set_manual_strategy(self, strategy_id):
        """تعيين استراتيجية يدوياً"""
        if strategy_id in self.strategies and self.strategies[strategy_id]['enabled']:
            self.manual_strategy = strategy_id
            self.auto_mode = False
            return True
        return False

    def enable_auto_mode(self):
        """تفعيل الوضع التلقائي"""
        self.auto_mode = True
        self.manual_strategy = None

    def get_strategies_for_execution(self):
        """الحصول على الاستراتيجيات للتنفيذ"""
        if not self.auto_mode and self.manual_strategy:
            # وضع يدوي - استراتيجية واحدة فقط
            strategy_info = self.strategies[self.manual_strategy]
            if strategy_info['class']:
                return [strategy_info['class']()]
            return []
        else:
            # وضع تلقائي - جميع الاستراتيجيات المفعلة
            strategies = []
            for strategy_info in self.strategies.values():
                if strategy_info['enabled'] and strategy_info['class']:
                    strategies.append(strategy_info['class']())
            return strategies


class WebScraper:
    """كلاس لاستخراج البيانات من المواقع مع استراتيجيات متعددة"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        self.parser = QueryParser()
        self.strategy_manager = StrategyManager()
        self.strategies = []  # سيتم تحديثها من المدير
        self.best_strategy = None

    def get_available_strategies(self):
        """الحصول على قائمة الاستراتيجيات المتاحة"""
        return self.strategy_manager.get_available_strategies()

    def set_strategy(self, strategy_id):
        """تعيين استراتيجية محددة يدوياً"""
        return self.strategy_manager.set_manual_strategy(strategy_id)

    def enable_auto_strategy(self):
        """تفعيل الوضع التلقائي للاستراتيجيات"""
        self.strategy_manager.enable_auto_mode()

    def get_current_strategy_mode(self):
        """الحصول على معلومات الوضع الحالي للاستراتيجيات"""
        return {
            'auto_mode': self.strategy_manager.auto_mode,
            'manual_strategy': self.strategy_manager.manual_strategy,
            'available_strategies': self.get_available_strategies()
        }
    
    def scrape_website(self, url, query, max_pages=1, page_load_delay=1):
        """استخراج بيانات الأفلام والمسلسلات من الموقع مع دعم الترميز العربي"""
        try:
            print(f"🔍 تحليل الاستعلام: {query}")
            # تحليل الاستعلام
            parsed_query = self.parser.parse_query(query)
            print(f"📋 الحقول المطلوبة: {parsed_query.get('fields', [])}")

            print(f"🌐 جلب الصفحة: {url}")
            # جلب الصفحة مع ضمان الترميز الصحيح
            response = self._fetch_page_with_encoding(url, page_load_delay)
            print(f"✅ تم جلب الصفحة بنجاح ({len(response.content)} بايت، الترميز: {response.encoding})")

            # تحليل HTML مع الترميز الصحيح
            soup = BeautifulSoup(response.content, 'html5lib', from_encoding=response.encoding)
            print(f"🔧 تم تحليل HTML")

            # استخراج البيانات
            print(f"⚙️ بدء استخراج البيانات...")
            results = self._extract_data(soup, parsed_query, url)

            # تنظيف وإصلاح النصوص العربية
            results = self._fix_arabic_encoding(results)
            print(f"📊 تم استخراج {len(results)} عنصر")

            return results

        except requests.exceptions.RequestException as e:
            raise Exception(f"خطأ في الاتصال بالموقع: {str(e)}")
        except Exception as e:
            raise Exception(f"خطأ في استخراج البيانات: {str(e)}")

    def _fetch_page_with_encoding(self, url, page_load_delay=1):
        """جلب الصفحة مع ضمان الترميز الصحيح - مبسط"""
        response = self.session.get(url, timeout=30)
        response.raise_for_status()

        # تأخير إضافي للسماح بتحميل المحتوى الديناميكي
        if page_load_delay > 0:
            print(f"⏳ انتظار {page_load_delay} ثانية لتحميل المحتوى...")
            time.sleep(page_load_delay)

        # ترك requests يحدد الترميز تلقائياً
        print(f"✅ تم جلب الصفحة بنجاح ({len(response.content)} بايت، الترميز: {response.encoding or 'تلقائي'})")

        return response

    def _detect_encoding(self, content, headers):
        """تحديد ترميز الصفحة"""
        import re

        # البحث في headers
        content_type = headers.get('content-type', '').lower()
        if 'charset=' in content_type:
            charset = content_type.split('charset=')[1].split(';')[0].strip()
            if charset:
                return charset

        # البحث في meta tags
        try:
            # فحص أول 2KB من المحتوى للعثور على meta charset
            head_content = content[:2048].decode('utf-8', errors='ignore')

            # البحث عن meta charset
            charset_match = re.search(r'<meta[^>]+charset["\s]*=["\s]*([^">\s]+)', head_content, re.IGNORECASE)
            if charset_match:
                return charset_match.group(1)

            # البحث عن meta http-equiv
            http_equiv_match = re.search(r'<meta[^>]+http-equiv["\s]*=["\s]*["\']?content-type["\']?[^>]+content["\s]*=["\s]*["\']?[^"\']*charset=([^"\'>\s]+)', head_content, re.IGNORECASE)
            if http_equiv_match:
                return http_equiv_match.group(1)

        except:
            pass

        # افتراضي UTF-8 للمحتوى العربي
        return 'utf-8'

    def _fix_arabic_encoding(self, results):
        """إصلاح ترميز النصوص العربية"""
        import html

        fixed_results = []
        for item in results:
            fixed_item = {}
            for key, value in item.items():
                if isinstance(value, str) and value:
                    # إصلاح HTML entities
                    fixed_value = html.unescape(value)

                    # إصلاح الترميز المختلط
                    try:
                        # محاولة إصلاح الترميز إذا كان معطوباً
                        if '?' in fixed_value or 'Ø' in fixed_value or 'Ù' in fixed_value:
                            # محاولة فك الترميز وإعادة ترميزه
                            try:
                                fixed_value = fixed_value.encode('latin1').decode('utf-8')
                            except:
                                try:
                                    fixed_value = fixed_value.encode('cp1256').decode('utf-8')
                                except:
                                    pass  # الاحتفاظ بالقيمة الأصلية
                    except:
                        pass

                    # تنظيف المسافات الزائدة
                    fixed_value = ' '.join(fixed_value.split())
                    fixed_item[key] = fixed_value
                else:
                    fixed_item[key] = value

            fixed_results.append(fixed_item)

        return fixed_results

    def scrape_multiple_pages(self, pattern_info, query, max_pages, start_page=1, progress_callback=None, page_load_delay=2):
        """استخراج البيانات من صفحات متتالية مع تتبع التقدم"""
        try:
            print(f"🚀 بدء الاستخراج المتتالي من {max_pages} صفحة")
            print(f"📊 النمط: {pattern_info['pattern']}")
            print(f"🔢 البدء من الصفحة: {start_page}")

            all_results = []
            successful_pages = 0
            failed_pages = 0

            # تحليل الاستعلام مرة واحدة
            parsed_query = self.parser.parse_query(query)

            # توليد روابط الصفحات
            analyzer = PagePatternAnalyzer()
            page_urls = analyzer.generate_page_urls(pattern_info, start_page, max_pages)

            for i, page_url in enumerate(page_urls):
                current_page = start_page + i

                try:
                    if progress_callback:
                        progress_callback(f"📄 معالجة الصفحة {i + 1}/{max_pages}: {page_url}")

                    print(f"🌐 جلب الصفحة {current_page}: {page_url}")

                    # جلب الصفحة مع إعادة المحاولة والترميز الصحيح
                    response = self._fetch_page_with_encoding(page_url, page_load_delay)

                    if response:
                        # تحليل HTML مع الترميز الصحيح
                        soup = BeautifulSoup(response.content, 'html5lib', from_encoding=response.encoding)

                        # استخراج البيانات وإصلاح الترميز
                        page_results = self._extract_data(soup, parsed_query, page_url)
                        page_results = self._fix_arabic_encoding(page_results)

                        if page_results:
                            all_results.extend(page_results)
                            successful_pages += 1

                            if progress_callback:
                                progress_callback(f"✅ الصفحة {i + 1}: تم استخراج {len(page_results)} عنصر")

                            print(f"✅ الصفحة {current_page}: {len(page_results)} عنصر")
                        else:
                            if progress_callback:
                                progress_callback(f"⚠️ الصفحة {i + 1}: لم يتم العثور على بيانات")
                            print(f"⚠️ الصفحة {current_page}: لا توجد بيانات")
                    else:
                        failed_pages += 1
                        if progress_callback:
                            progress_callback(f"❌ فشل في جلب الصفحة {i + 1}")
                        print(f"❌ فشل في جلب الصفحة {current_page}")

                except Exception as e:
                    failed_pages += 1
                    error_msg = f"❌ خطأ في الصفحة {current_page}: {str(e)}"
                    if progress_callback:
                        progress_callback(error_msg)
                    print(error_msg)

                # توقف قصير بين الصفحات لتجنب الحظر
                if i < len(page_urls) - 1:
                    if progress_callback:
                        progress_callback("⏳ انتظار قصير قبل الصفحة التالية...")
                    time.sleep(2)  # توقف لمدة ثانيتين

            # تقرير نهائي
            total_items = len(all_results)
            movies = len([item for item in all_results if item.get('type') == 'movie'])
            series = len([item for item in all_results if item.get('type') == 'series'])

            summary = {
                'total_items': total_items,
                'movies_count': movies,
                'series_count': series,
                'successful_pages': successful_pages,
                'failed_pages': failed_pages,
                'total_pages': max_pages
            }

            if progress_callback:
                progress_callback(f"🎉 انتهى الاستخراج! إجمالي: {total_items} عنصر من {successful_pages} صفحة")
                if movies > 0:
                    progress_callback(f"🎬 أفلام: {movies}")
                if series > 0:
                    progress_callback(f"📺 مسلسلات: {series}")

            print(f"🎉 انتهى الاستخراج المتتالي:")
            print(f"📊 إجمالي العناصر: {total_items}")
            print(f"🎬 أفلام: {movies}")
            print(f"📺 مسلسلات: {series}")
            print(f"✅ صفحات ناجحة: {successful_pages}")
            print(f"❌ صفحات فاشلة: {failed_pages}")

            return all_results, summary

        except Exception as e:
            error_msg = f"خطأ في الاستخراج المتتالي: {str(e)}"
            if progress_callback:
                progress_callback(f"❌ {error_msg}")
            raise Exception(error_msg)

    def _fetch_with_retry(self, url, max_retries=3, delay=1):
        """جلب الصفحة مع إعادة المحاولة في حالة الفشل"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                print(f"⚠️ محاولة {attempt + 1}/{max_retries} فشلت: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(delay * (attempt + 1))  # زيادة وقت الانتظار مع كل محاولة
                else:
                    print(f"❌ فشل في جلب الصفحة بعد {max_retries} محاولات")
                    return None
    
    def _extract_data(self, soup, parsed_query, base_url):
        """استخراج البيانات باستخدام استراتيجيات متعددة"""
        print("🔍 بدء تجربة الاستراتيجيات المختلفة...")

        best_results = []
        best_strategy = None
        best_score = 0.0

        # إذا كان هناك selector محدد، جرب استخدامه أولاً
        if parsed_query.get('selector'):
            print(f"🎯 تجربة selector محدد: {parsed_query['selector']}")
            elements = soup.select(parsed_query['selector'])
            if elements:
                results = []
                for element in elements:
                    item = self._extract_movie_data_legacy(element, parsed_query['fields'], base_url)
                    if item and (item.get('title') or item.get('link')):
                        results.append(item)

                if results:
                    print(f"✅ Selector محدد: {len(results)} عنصر")
                    return results

        # الحصول على الاستراتيجيات من المدير
        self.strategies = self.strategy_manager.get_strategies_for_execution()

        # تجربة جميع الاستراتيجيات
        for strategy in self.strategies:
            print(f"🧪 تجربة استراتيجية: {strategy.name}")

            try:
                results = strategy.extract_data(soup, base_url)

                if results:
                    # تقييم الاستراتيجية
                    score = strategy.evaluate_success(results)
                    print(f"📊 {strategy.name}: {len(results)} عنصر، نقاط الجودة: {score:.2f}")

                    # اختيار أفضل استراتيجية
                    if score > best_score:
                        best_score = score
                        best_results = results
                        best_strategy = strategy
                else:
                    print(f"❌ {strategy.name}: لم تجد نتائج")

            except Exception as e:
                print(f"❌ خطأ في {strategy.name}: {str(e)}")
                continue

        # حفظ أفضل استراتيجية للاستخدام المستقبلي
        if best_strategy:
            self.best_strategy = best_strategy
            print(f"🏆 أفضل استراتيجية: {best_strategy.name} ({len(best_results)} عنصر)")

            # تحسين البيانات المستخرجة
            enhanced_results = []
            for item in best_results:
                enhanced_item = self._enhance_item_data(item, base_url)
                enhanced_results.append(enhanced_item)

            return enhanced_results

        # إذا فشلت جميع الاستراتيجيات، استخدم الطريقة التقليدية
        print("⚠️ فشلت جميع الاستراتيجيات، استخدام الطريقة التقليدية...")
        return self._extract_data_fallback(soup, parsed_query, base_url)

    def _enhance_item_data(self, item, base_url):
        """تحسين بيانات العنصر المستخرج"""
        enhanced_item = item.copy()

        # تنسيق البيانات حسب النوع
        content_type = item.get('type', 'movie')

        if content_type == 'series':
            if item.get('title'):
                enhanced_item['series_name'] = item['title']
            if item.get('image'):
                enhanced_item['series_img'] = item['image']
            if item.get('link'):
                enhanced_item['series_href'] = item['link']
        else:
            if item.get('title'):
                enhanced_item['movies_name'] = item['title']
            if item.get('image'):
                enhanced_item['movies_img'] = item['image']
            if item.get('link'):
                enhanced_item['movies_href'] = item['link']

        # إضافة الحقول العامة للتوافق
        if item.get('image'):
            enhanced_item['imageUrl'] = item['image']

        return enhanced_item

    def _extract_data_fallback(self, soup, parsed_query, base_url):
        """الطريقة التقليدية كخطة احتياطية - محسنة ومطورة"""
        results = []

        print("🔄 بدء الطريقة التقليدية المحسنة...")

        # 1. البحث في جميع الروابط المحتملة للأفلام
        all_links = soup.find_all('a', href=True)
        image_links = []

        print(f"🔍 فحص {len(all_links)} رابط...")

        for link in all_links:
            is_movie_link = False

            # البحث عن صور في الرابط
            if link.find('img'):
                is_movie_link = True

            # البحث عن كلمات مفتاحية في النص أو الرابط
            text = link.get_text().lower()
            href = link.get('href', '').lower()
            movie_keywords = ['فيلم', 'movie', 'film', 'watch', 'مشاهدة', 'مترجم']

            if any(keyword in text or keyword in href for keyword in movie_keywords):
                is_movie_link = True

            # البحث في الـ classes
            classes = ' '.join(link.get('class', [])).lower()
            if any(cls in classes for cls in ['movie', 'film', 'video', 'post']):
                is_movie_link = True

            if is_movie_link:
                image_links.append(link)

        print(f"🔍 وجدت {len(image_links)} رابط محتمل للأفلام")

        for i, link in enumerate(image_links):
            try:
                item = self._extract_comprehensive_movie_data(link, base_url)
                if item and (item.get('title') or item.get('link')):
                    results.append(item)
                    if i < 3:  # عرض أول 3 نتائج للتشخيص
                        print(f"✅ استخراج ناجح {i+1}: {item.get('title', 'بدون عنوان')}")
            except Exception as e:
                if i < 3:
                    print(f"❌ خطأ في استخراج {i+1}: {e}")
                continue

        # 2. إذا لم نجد نتائج كافية، ابحث في العناصر الأخرى
        if len(results) < 5:
            print("🔍 البحث في العناصر الأخرى...")

            # البحث في divs مع classes محددة
            potential_selectors = [
                '.post__image',  # للموقع الأول
                '.pm-video-thumb',  # للمواقع الأخرى
                '.thumbnail',
                'div[class*="movie"]',
                'div[class*="film"]',
                'div[class*="video"]',
                'div[class*="post"]'
            ]

            for selector in potential_selectors:
                try:
                    elements = soup.select(selector)
                    print(f"🔍 وجدت {len(elements)} عنصر مع {selector}")

                    for elem in elements[:20]:  # أول 20 عنصر
                        # البحث عن رابط في العنصر أو والده
                        link_elem = elem if elem.name == 'a' else elem.find_parent('a')
                        if not link_elem:
                            link_elem = elem.find('a')

                        if link_elem and link_elem.get('href'):
                            item = self._extract_comprehensive_movie_data(link_elem, base_url)
                            if item and (item.get('title') or item.get('image')):
                                # تجنب التكرار
                                if not any(existing.get('link') == item.get('link') for existing in results):
                                    results.append(item)

                    if results:
                        break  # إذا وجدنا نتائج، توقف

                except Exception as e:
                    continue

        print(f"🔄 الطريقة التقليدية: {len(results)} عنصر")
        return results

    def _extract_comprehensive_movie_data(self, element, base_url):
        """استخراج شامل لبيانات الفيلم من عنصر HTML - محسن"""
        item = {}

        try:
            # 1. استخراج الرابط باستخدام الوظيفة المحسنة
            link_url = self._find_link_comprehensive(element, base_url)
            if link_url:
                item['link'] = link_url

            # 2. استخراج العنوان من مصادر متعددة
            title = None

            # من الصورة alt/title
            img = element.find('img')
            if img:
                title = img.get('alt') or img.get('title') or img.get('data-title')

            # من attributes العنصر نفسه
            if not title:
                title = element.get('title') or element.get('alt') or element.get('data-title')

            # من العناوين الفرعية
            if not title:
                for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    heading = element.find(tag)
                    if heading:
                        title = heading.get_text(strip=True)
                        break

            # من النص المباشر (مع تحسينات)
            if not title:
                text = element.get_text(strip=True)
                if text and 3 <= len(text) <= 200:
                    # تنظيف النص وأخذ السطر الأول المفيد
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    for line in lines:
                        if 3 <= len(line) <= 100 and any(char.isalpha() for char in line):
                            title = line
                            break

            if title:
                # تنظيف العنوان
                title = self._fix_arabic_encoding(title)
                title = title.strip()
                if title and len(title) > 2:
                    item['title'] = title

            # 3. استخراج الصورة
            image_url = None
            if img:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy') or img.get('data-original')
                if src:
                    if src.startswith('http'):
                        image_url = src
                    elif src.startswith('//'):
                        image_url = 'https:' + src
                    elif src.strip():  # تجنب القيم الفارغة
                        image_url = urljoin(base_url, src)

            if image_url:
                item['image'] = image_url

            # 4. تحديد نوع المحتوى
            title_text = item.get('title', '').lower()
            series_keywords = ['series', 'season', 'episode', 'مسلسل', 'موسم', 'حلقة', 'مسلسلات']

            if any(keyword in title_text for keyword in series_keywords):
                item['type'] = 'series'
            else:
                item['type'] = 'movie'

            # إرجاع النتيجة فقط إذا كان لدينا عنوان أو رابط على الأقل
            if item.get('title') or item.get('link'):
                return item
            else:
                return None

        except Exception as e:
            # print(f"❌ خطأ في استخراج البيانات الشامل: {e}")
            return None

    def _extract_movie_data_legacy(self, element, fields, base_url):
        """استخراج بيانات فيلم أو مسلسل من عنصر HTML"""
        item = {}

        # استخراج البيانات الأساسية
        title = self._find_movie_title(element)
        link = self._find_movie_link(element, base_url)
        image = self._find_movie_image(element, base_url)

        # تحديد نوع المحتوى (فيلم أم مسلسل)
        content_type = self._detect_content_type(element, title)

        # تنسيق البيانات حسب النوع
        if content_type == 'series':
            # تنسيق المسلسلات
            if title:
                item['series_name'] = title
            if image:
                item['series_img'] = image
            if link:
                item['series_href'] = link
            item['type'] = 'series'
        else:
            # تنسيق الأفلام (افتراضي)
            if title:
                item['movies_name'] = title
            if image:
                item['movies_img'] = image
            if link:
                item['movies_href'] = link
            item['type'] = 'movie'

        # إضافة الحقول العامة للتوافق مع الاستعلامات
        if title:
            item['title'] = title
        if link:
            item['link'] = link
        if image:
            item['imageUrl'] = image
            item['image'] = image  # للتوافق مع النظام القديم

        return item

    def _find_movie_title(self, element):
        """البحث عن عنوان الفيلم في العنصر"""
        title_sources = []

        # 1. البحث في attributes
        for attr in ['title', 'alt', 'data-title', 'data-name', 'aria-label']:
            if element.get(attr):
                title_sources.append(element.get(attr))

        # 2. البحث في الصور الفرعية
        img = element.find('img')
        if img:
            for attr in ['alt', 'title', 'data-title']:
                if img.get(attr):
                    title_sources.append(img.get(attr))

        # 3. البحث في العناصر النصية الفرعية
        text_selectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            '.title', '.name', '.movie-title', '.film-title',
            '.movie-name', '.film-name', '.post-title',
            'span[class*="title"]', 'div[class*="title"]',
            'p[class*="title"]', 'a[class*="title"]'
        ]

        for selector in text_selectors:
            try:
                sub_element = element.select_one(selector)
                if sub_element:
                    text = sub_element.get_text(strip=True)
                    if text and len(text) < 200:
                        title_sources.append(text)
            except:
                continue

        # 4. البحث في النص المباشر (كحل أخير)
        direct_text = element.get_text(strip=True)
        if direct_text and len(direct_text) < 100:  # نص قصير فقط
            # تنظيف النص من الأسطر الجديدة والمسافات الزائدة
            clean_text = ' '.join(direct_text.split())
            if clean_text:
                title_sources.append(clean_text)

        # إرجاع أول عنوان صالح
        for title in title_sources:
            if title and len(title.strip()) > 2:  # على الأقل 3 أحرف
                clean_title = title.strip()
                # تجنب النصوص التي تحتوي على كلمات غير مرغوبة
                unwanted = ['click', 'more', 'read', 'view', 'watch', 'download']
                if not any(word in clean_title.lower() for word in unwanted):
                    return clean_title

        return None

    def _find_movie_link(self, element, base_url):
        """البحث عن رابط الفيلم مع تحسينات لتجنب الروابط الخاطئة"""

        # قائمة الروابط المستبعدة
        excluded_patterns = [
            '#modal-login-form',
            'categories.php',
            'page=',
            'order=',
            'javascript:',
            'mailto:',
            '#',
            'login',
            'register',
            'signup'
        ]

        # كلمات مفتاحية للروابط المفضلة
        preferred_keywords = ['watch', 'view', 'play', 'movie', 'film', 'video', 'vid=']

        def is_valid_link(href):
            """فحص صحة الرابط"""
            if not href or href == '#':
                return False

            href_lower = href.lower()

            # استبعاد الروابط غير المرغوبة
            for pattern in excluded_patterns:
                if pattern in href_lower:
                    return False

            return True

        def get_link_priority(href):
            """حساب أولوية الرابط"""
            if not href:
                return 0

            href_lower = href.lower()
            priority = 1

            # أولوية عالية جداً لموقع shahidwbas.tv مع watch.php
            if 'shahidwbas.tv' in href_lower and 'watch.php' in href_lower:
                priority += 20

            # أولوية عالية للروابط التي تحتوي على كلمات مفتاحية
            for keyword in preferred_keywords:
                if keyword in href_lower:
                    priority += 10
                    break

            # أولوية إضافية للروابط التي تحتوي على معرف الفيديو
            if 'vid=' in href_lower or 'id=' in href_lower:
                priority += 5

            return priority

        # 1. إذا كان العنصر نفسه رابط
        if element.name == 'a' and element.get('href'):
            href = element.get('href')
            if is_valid_link(href):
                return urljoin(base_url, href)

        # 2. جمع جميع الروابط الفرعية وترتيبها حسب الأولوية
        all_links = element.find_all('a', href=True)
        valid_links = []

        for link in all_links:
            href = link.get('href')
            if is_valid_link(href):
                priority = get_link_priority(href)
                valid_links.append((href, priority, link))

        # ترتيب الروابط حسب الأولوية (الأعلى أولاً)
        valid_links.sort(key=lambda x: x[1], reverse=True)

        # 3. إرجاع أفضل رابط
        if valid_links:
            best_href = valid_links[0][0]
            return urljoin(base_url, best_href)

        # 4. البحث في data attributes
        data_attrs = ['data-href', 'data-url', 'data-link', 'data-target', 'data-src']
        for attr in data_attrs:
            data_link = element.get(attr)
            if is_valid_link(data_link):
                return urljoin(base_url, data_link)

        # 5. البحث في onclick events
        onclick = element.get('onclick', '')
        if onclick:
            import re
            # البحث عن روابط في onclick
            url_match = re.search(r'["\']([^"\']*(?:watch|view|play|\.php\?vid=)[^"\']*)["\']', onclick)
            if url_match:
                potential_url = url_match.group(1)
                if is_valid_link(potential_url):
                    return urljoin(base_url, potential_url)

        return None

    def _find_movie_image(self, element, base_url):
        """البحث عن صورة الفيلم"""
        # 1. إذا كان العنصر نفسه صورة
        if element.name == 'img':
            src = element.get('src') or element.get('data-src') or element.get('data-original')
            if src:
                return urljoin(base_url, src)

        # 2. البحث عن صورة في العناصر الفرعية
        img_elements = element.find_all('img')
        for img in img_elements:
            # البحث في src أولاً
            src = img.get('src')
            if src and not src.startswith('data:') and 'placeholder' not in src.lower():
                return urljoin(base_url, src)

            # البحث في data attributes للـ lazy loading
            for attr in ['data-src', 'data-original', 'data-lazy', 'data-image']:
                src = img.get(attr)
                if src and not src.startswith('data:'):
                    return urljoin(base_url, src)

        # 3. البحث في background-image في style
        style = element.get('style', '')
        if 'background-image' in style:
            import re
            match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
            if match:
                img_url = match.group(1)
                if not img_url.startswith('data:'):
                    return urljoin(base_url, img_url)

        # 4. البحث في العناصر الفرعية التي لها background-image
        for child in element.find_all(['div', 'span', 'section']):
            child_style = child.get('style', '')
            if 'background-image' in child_style:
                import re
                match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', child_style)
                if match:
                    img_url = match.group(1)
                    if not img_url.startswith('data:'):
                        return urljoin(base_url, img_url)

        # 5. البحث في data attributes للعنصر نفسه
        data_attrs = ['data-src', 'data-image', 'data-poster', 'data-thumbnail', 'data-bg']
        for attr in data_attrs:
            data_img = element.get(attr)
            if data_img and not data_img.startswith('data:'):
                return urljoin(base_url, data_img)

        # 6. البحث في CSS classes التي قد تحتوي على صور
        class_list = element.get('class', [])
        if class_list:
            # أحياناً تكون الصورة في CSS class
            for cls in class_list:
                if 'bg-' in cls or 'background-' in cls:
                    # هذا يتطلب معرفة CSS، لكن يمكن تجربة patterns شائعة
                    pass

        return None
    
    def _detect_content_type(self, element, title=None):
        """تحديد نوع المحتوى (فيلم أو مسلسل) بدقة أكبر"""
        # جمع النصوص للتحليل
        texts_to_check = []

        # إضافة العنوان
        if title:
            texts_to_check.append(title.lower())

        # إضافة نص العنصر
        element_text = element.get_text().lower()
        texts_to_check.append(element_text)

        # إضافة classes
        classes = ' '.join(element.get('class', [])).lower()
        texts_to_check.append(classes)

        # إضافة attributes مهمة
        for attr in ['id', 'data-type', 'data-category']:
            attr_value = element.get(attr, '')
            if attr_value:
                texts_to_check.append(str(attr_value).lower())

        # دمج جميع النصوص
        combined_text = ' '.join(texts_to_check)

        # كلمات مفتاحية للمسلسلات
        series_keywords = [
            'مسلسل', 'مسلسلات', 'الموسم', 'موسم', 'الحلقة', 'حلقة', 'سيزون',
            'series', 'tv show', 'tv series', 'season', 'episode', 'episodes',
            'tv', 'show', 'serial', 'drama', 'web series'
        ]

        # كلمات مفتاحية للأفلام
        movie_keywords = [
            'فيلم', 'أفلام', 'فلم', 'سينما', 'سينمائي',
            'movie', 'movies', 'film', 'films', 'cinema'
        ]

        # حساب النقاط
        series_score = sum(1 for keyword in series_keywords if keyword in combined_text)
        movie_score = sum(1 for keyword in movie_keywords if keyword in combined_text)

        # تحديد النوع
        if series_score > movie_score:
            return 'series'
        elif movie_score > series_score:
            return 'movie'
        else:
            # افتراضي: فيلم
            return 'movie'
    



class PatternAnalysisStrategy:
    """كلاس أساسي لاستراتيجيات تحليل الأنماط"""

    def __init__(self, name, description):
        self.name = name
        self.description = description
        self.confidence = 0.0

    def analyze(self, pages):
        """تحليل النمط - يجب تنفيذها في الكلاسات الفرعية"""
        raise NotImplementedError

    def calculate_confidence(self, pattern_info):
        """حساب مستوى الثقة في النمط المكتشف"""
        if not pattern_info or not pattern_info.get('pattern'):
            self.confidence = 0.0
            return self.confidence

        confidence_factors = []

        # عامل الاتساق في الخطوة
        if 'step' in pattern_info and pattern_info['step'] > 0:
            confidence_factors.append(0.3)

        # عامل وجود أرقام متتالية
        if 'base_numbers' in pattern_info and len(pattern_info['base_numbers']) >= 2:
            numbers = pattern_info['base_numbers']
            if len(set(numbers)) == len(numbers):  # أرقام فريدة
                confidence_factors.append(0.4)

        # عامل نوع النمط
        pattern_type = pattern_info.get('pattern_type', '')
        if pattern_type in ['numeric_simple', 'query_parameter']:
            confidence_factors.append(0.3)
        elif pattern_type == 'path_segment':
            confidence_factors.append(0.2)

        self.confidence = sum(confidence_factors)
        return self.confidence


class NumericPatternStrategy(PatternAnalysisStrategy):
    """استراتيجية تحليل الأنماط الرقمية"""

    def __init__(self):
        super().__init__("Numeric Pattern", "تحليل الأنماط الرقمية البسيطة والمعقدة")

    def analyze(self, pages):
        """تحليل الأنماط الرقمية المختلفة"""
        try:
            # تحليل الأنماط الرقمية البسيطة
            simple_result = self._analyze_simple_numeric(pages)
            if simple_result['pattern']:
                return simple_result

            # تحليل الأنماط الرقمية المعقدة
            complex_result = self._analyze_complex_numeric(pages)
            if complex_result['pattern']:
                return complex_result

            return {'pattern': None, 'error': 'لم يتم العثور على نمط رقمي'}

        except Exception as e:
            return {'pattern': None, 'error': f'خطأ في تحليل النمط الرقمي: {str(e)}'}

    def _analyze_simple_numeric(self, pages):
        """تحليل الأنماط الرقمية البسيطة"""
        numbers = []
        base_patterns = []

        for page in pages:
            # البحث عن جميع الأرقام
            page_numbers = re.findall(r'\d+', page)
            if page_numbers:
                # تجربة أرقام مختلفة (آخر رقم، أكبر رقم، إلخ)
                candidates = [
                    int(page_numbers[-1]),  # آخر رقم
                    max([int(n) for n in page_numbers]),  # أكبر رقم
                ]

                for candidate in candidates:
                    if candidate not in [n[0] if isinstance(n, tuple) else n for n in numbers]:
                        numbers.append((candidate, page))
                        # إنشاء النمط
                        pattern = re.sub(str(candidate), '{num}', page, count=1)
                        base_patterns.append(pattern)
                        break

        if len(numbers) >= 2:
            # فرز حسب الأرقام
            numbers.sort(key=lambda x: x[0])

            # حساب الخطوات
            steps = []
            for i in range(1, len(numbers)):
                step = numbers[i][0] - numbers[i-1][0]
                steps.append(step)

            # التحقق من ثبات الخطوة
            if len(set(steps)) == 1 and steps[0] > 0:
                step = steps[0]

                # إنشاء النمط النهائي
                first_num = numbers[0][0]
                first_page = numbers[0][1]
                pattern = re.sub(str(first_num), '{num}', first_page, count=1)

                return {
                    'pattern': pattern,
                    'variable': 'page_number',
                    'step': step,
                    'base_numbers': [n[0] for n in numbers],
                    'pattern_type': 'numeric_simple',
                    'start_number': first_num
                }

        return {'pattern': None, 'error': 'نمط رقمي غير ثابت'}

    def _analyze_complex_numeric(self, pages):
        """تحليل الأنماط الرقمية المعقدة (أرقام متعددة في الصفحة)"""
        try:
            # تحليل كل موضع رقمي محتمل
            all_numbers = []
            for page in pages:
                page_numbers = re.findall(r'\d+', page)
                all_numbers.append([int(n) for n in page_numbers])

            # العثور على الموضع المتغير
            if all(len(nums) == len(all_numbers[0]) for nums in all_numbers):
                num_positions = len(all_numbers[0])

                for pos in range(num_positions):
                    position_numbers = [nums[pos] for nums in all_numbers]

                    # التحقق من التسلسل
                    if len(set(position_numbers)) == len(position_numbers):
                        position_numbers.sort()
                        steps = [position_numbers[i] - position_numbers[i-1]
                                for i in range(1, len(position_numbers))]

                        if len(set(steps)) == 1 and steps[0] > 0:
                            # إنشاء النمط
                            pattern = pages[0]
                            original_num = all_numbers[0][pos]
                            pattern = re.sub(str(original_num), '{num}', pattern, count=1)

                            return {
                                'pattern': pattern,
                                'variable': f'number_position_{pos}',
                                'step': steps[0],
                                'base_numbers': position_numbers,
                                'pattern_type': 'numeric_complex',
                                'start_number': min(position_numbers)
                            }

            return {'pattern': None, 'error': 'نمط رقمي معقد غير مكتشف'}

        except Exception as e:
            return {'pattern': None, 'error': f'خطأ في تحليل النمط المعقد: {str(e)}'}


class URLParameterStrategy(PatternAnalysisStrategy):
    """استراتيجية تحليل معاملات URL"""

    def __init__(self):
        super().__init__("URL Parameter", "تحليل معاملات الاستعلام في الروابط")

    def analyze(self, pages):
        """تحليل معاملات URL"""
        try:
            from urllib.parse import urlparse, parse_qs, urlencode

            parsed_urls = []
            for page in pages:
                parsed = urlparse(page)
                query_params = parse_qs(parsed.query)
                parsed_urls.append({
                    'base_url': f"{parsed.scheme}://{parsed.netloc}{parsed.path}",
                    'params': query_params,
                    'fragment': parsed.fragment,
                    'original': page
                })

            # البحث عن معاملات مشتركة
            if not parsed_urls:
                return {'pattern': None, 'error': 'لا توجد معاملات URL'}

            common_params = set(parsed_urls[0]['params'].keys())
            for parsed in parsed_urls[1:]:
                common_params &= set(parsed['params'].keys())

            # تحليل كل معامل مشترك
            for param in common_params:
                values = []
                for parsed in parsed_urls:
                    param_value = parsed['params'][param][0] if parsed['params'][param] else ''
                    if param_value.isdigit():
                        values.append(int(param_value))

                if len(values) == len(pages) and len(set(values)) == len(values):
                    # التحقق من التسلسل
                    values.sort()
                    steps = [values[i] - values[i-1] for i in range(1, len(values))]

                    if len(set(steps)) == 1 and steps[0] > 0:
                        step = steps[0]
                        base_url = parsed_urls[0]['base_url']

                        # إعادة بناء النمط
                        sample_params = parsed_urls[0]['params'].copy()
                        sample_params[param] = ['{num}']

                        # تحويل المعاملات إلى string
                        param_strings = []
                        for p, v in sample_params.items():
                            if p == param:
                                param_strings.append(f"{p}={{num}}")
                            else:
                                param_strings.append(f"{p}={v[0]}")

                        pattern = f"{base_url}?{'&'.join(param_strings)}"

                        return {
                            'pattern': pattern,
                            'variable': param,
                            'step': step,
                            'base_numbers': values,
                            'pattern_type': 'query_parameter',
                            'start_number': min(values)
                        }

            return {'pattern': None, 'error': 'لم يتم العثور على معاملات متسلسلة'}

        except Exception as e:
            return {'pattern': None, 'error': f'خطأ في تحليل معاملات URL: {str(e)}'}


class PagePatternAnalyzer:
    """كلاس لتحليل أنماط الصفحات المحسن مع استراتيجيات متعددة"""

    def __init__(self):
        self.strategies = [
            NumericPatternStrategy(),
            URLParameterStrategy(),
            # يمكن إضافة المزيد من الاستراتيجيات
        ]
        self.best_strategy = None

    def analyze_pattern(self, pages):
        """تحليل نمط الصفحات باستخدام استراتيجيات متعددة"""
        if len(pages) < 2:
            return {'pattern': None, 'error': 'يجب توفير صفحتين على الأقل'}

        try:
            print(f"🔍 تحليل نمط الصفحات باستخدام {len(self.strategies)} استراتيجية")
            print(f"📄 الصفحات: {pages}")

            best_result = None
            best_confidence = 0.0
            best_strategy = None

            # تجربة جميع الاستراتيجيات
            for strategy in self.strategies:
                print(f"🧪 تجربة استراتيجية: {strategy.name}")

                try:
                    result = strategy.analyze(pages)

                    if result and result.get('pattern'):
                        confidence = strategy.calculate_confidence(result)
                        print(f"✅ {strategy.name}: نمط مكتشف، الثقة: {confidence:.2f}")

                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_result = result
                            best_strategy = strategy
                    else:
                        print(f"❌ {strategy.name}: {result.get('error', 'لم يتم اكتشاف نمط')}")

                except Exception as e:
                    print(f"❌ خطأ في {strategy.name}: {str(e)}")
                    continue

            # إرجاع أفضل نتيجة
            if best_result and best_strategy:
                self.best_strategy = best_strategy
                best_result['confidence'] = best_confidence
                best_result['strategy_used'] = best_strategy.name

                print(f"🏆 أفضل استراتيجية: {best_strategy.name} (ثقة: {best_confidence:.2f})")
                return best_result
            else:
                print("❌ فشلت جميع الاستراتيجيات في اكتشاف نمط")
                return {'pattern': None, 'error': 'لم تتمكن أي استراتيجية من اكتشاف نمط صالح'}

        except Exception as e:
            error_msg = f'خطأ في تحليل النمط: {str(e)}'
            print(f"❌ {error_msg}")
            return {'pattern': None, 'error': error_msg}

    def _analyze_advanced_pattern(self, pages):
        """تحليل متقدم للأنماط المختلفة"""

        # 1. تحليل الأنماط الرقمية البسيطة
        numeric_result = self._analyze_numeric_pattern(pages)
        if numeric_result['pattern']:
            return numeric_result

        # 2. تحليل أنماط المعاملات (query parameters)
        query_result = self._analyze_query_parameter_pattern(pages)
        if query_result['pattern']:
            return query_result

        # 3. تحليل أنماط المسارات المعقدة
        path_result = self._analyze_path_pattern(pages)
        if path_result['pattern']:
            return path_result

        return {'pattern': None, 'error': 'لم يتم العثور على نمط صالح'}

    def _analyze_numeric_pattern(self, pages):
        """تحليل الأنماط الرقمية البسيطة"""
        try:
            numbers = []
            base_patterns = []

            for page in pages:
                # البحث عن جميع الأرقام في الرابط
                page_numbers = re.findall(r'\d+', page)
                if page_numbers:
                    # أخذ آخر رقم (عادة رقم الصفحة)
                    numbers.append(int(page_numbers[-1]))
                    # استخراج النمط الأساسي
                    base_pattern = re.sub(r'\d+(?=(?:[^"]*"[^"]*")*[^"]*$)', '{num}', page)
                    base_patterns.append(base_pattern)

            if len(numbers) < 2:
                return {'pattern': None, 'error': 'لم يتم العثور على أرقام كافية'}

            # التحقق من التسلسل
            steps = []
            for i in range(1, len(numbers)):
                steps.append(numbers[i] - numbers[i-1])

            # التحقق من ثبات الخطوة
            if len(set(steps)) == 1:
                step = steps[0]

                # التحقق من ثبات النمط الأساسي
                if len(set(base_patterns)) == 1:
                    pattern = base_patterns[0]

                    return {
                        'pattern': pattern,
                        'variable': 'page_number',
                        'step': step,
                        'base_numbers': numbers,
                        'pattern_type': 'numeric_simple'
                    }

            return {'pattern': None, 'error': 'النمط الرقمي غير ثابت'}

        except Exception as e:
            return {'pattern': None, 'error': f'خطأ في تحليل النمط الرقمي: {str(e)}'}

    def _analyze_query_parameter_pattern(self, pages):
        """تحليل أنماط معاملات الاستعلام"""
        try:
            from urllib.parse import urlparse, parse_qs

            parsed_urls = []
            for page in pages:
                parsed = urlparse(page)
                query_params = parse_qs(parsed.query)
                parsed_urls.append({
                    'base_url': f"{parsed.scheme}://{parsed.netloc}{parsed.path}",
                    'params': query_params,
                    'fragment': parsed.fragment
                })

            # البحث عن معامل يحتوي على أرقام متتالية
            common_params = set(parsed_urls[0]['params'].keys())
            for parsed in parsed_urls[1:]:
                common_params &= set(parsed['params'].keys())

            for param in common_params:
                values = []
                for parsed in parsed_urls:
                    param_value = parsed['params'][param][0] if parsed['params'][param] else ''
                    if param_value.isdigit():
                        values.append(int(param_value))

                if len(values) == len(pages) and len(set(values)) == len(values):
                    # التحقق من التسلسل
                    values.sort()
                    steps = [values[i] - values[i-1] for i in range(1, len(values))]

                    if len(set(steps)) == 1:
                        step = steps[0]
                        base_url = parsed_urls[0]['base_url']

                        # بناء النمط
                        other_params = []
                        for p, v in parsed_urls[0]['params'].items():
                            if p != param:
                                other_params.append(f"{p}={v[0]}")

                        if other_params:
                            pattern = f"{base_url}?{param}={{num}}&{'&'.join(other_params)}"
                        else:
                            pattern = f"{base_url}?{param}={{num}}"

                        return {
                            'pattern': pattern,
                            'variable': param,
                            'step': step,
                            'base_numbers': values,
                            'pattern_type': 'query_parameter'
                        }

            return {'pattern': None, 'error': 'لم يتم العثور على نمط في معاملات الاستعلام'}

        except Exception as e:
            return {'pattern': None, 'error': f'خطأ في تحليل معاملات الاستعلام: {str(e)}'}

    def _analyze_path_pattern(self, pages):
        """تحليل أنماط المسارات المعقدة"""
        try:
            from urllib.parse import urlparse

            parsed_urls = [urlparse(page) for page in pages]

            # تحليل أجزاء المسار
            path_parts = []
            for parsed in parsed_urls:
                parts = [part for part in parsed.path.split('/') if part]
                path_parts.append(parts)

            # البحث عن الجزء المتغير
            if len(set(len(parts) for parts in path_parts)) == 1:  # نفس عدد الأجزاء
                num_parts = len(path_parts[0])

                for i in range(num_parts):
                    part_values = [parts[i] for parts in path_parts]

                    # التحقق من كون هذا الجزء رقمي ومتتالي
                    if all(part.isdigit() for part in part_values):
                        numbers = [int(part) for part in part_values]
                        numbers.sort()
                        steps = [numbers[j] - numbers[j-1] for j in range(1, len(numbers))]

                        if len(set(steps)) == 1:
                            step = steps[0]

                            # بناء النمط
                            base_url = f"{parsed_urls[0].scheme}://{parsed_urls[0].netloc}"
                            pattern_parts = path_parts[0].copy()
                            pattern_parts[i] = '{num}'
                            pattern = base_url + '/' + '/'.join(pattern_parts)

                            if parsed_urls[0].query:
                                pattern += f"?{parsed_urls[0].query}"

                            return {
                                'pattern': pattern,
                                'variable': f'path_part_{i}',
                                'step': step,
                                'base_numbers': numbers,
                                'pattern_type': 'path_segment'
                            }

            return {'pattern': None, 'error': 'لم يتم العثور على نمط في المسار'}

        except Exception as e:
            return {'pattern': None, 'error': f'خطأ في تحليل المسار: {str(e)}'}

    def generate_page_urls(self, pattern_info, start_page, num_pages):
        """توليد روابط الصفحات بناءً على النمط المكتشف"""
        try:
            urls = []
            pattern = pattern_info['pattern']
            step = pattern_info['step']

            for i in range(num_pages):
                page_number = start_page + (i * step)
                url = pattern.replace('{num}', str(page_number))
                urls.append(url)

            return urls

        except Exception as e:
            print(f"❌ خطأ في توليد الروابط: {str(e)}")
            return []


class QueryDebugger:
    """كلاس لتصحيح الاستعلامات"""
    
    def __init__(self):
        self.scraper = WebScraper()
    
    def debug_query(self, url, query):
        """تصحيح الاستعلام وإرجاع معلومات مفصلة"""
        try:
            # تحليل الاستعلام
            parsed_query = self.scraper.parser.parse_query(query)
            
            # جلب الصفحة
            response = self.scraper.session.get(url, timeout=30)
            response.raise_for_status()
            
            # تحليل HTML
            soup = BeautifulSoup(response.content, 'html5lib')
            
            # العثور على العناصر المطابقة
            if parsed_query['selector']:
                elements = soup.select(parsed_query['selector'])
            else:
                elements = soup.find_all(['a', 'div', 'span', 'h1', 'h2', 'h3', 'img'])
            
            # إنشاء معاينة HTML مع تمييز العناصر
            html_preview = self._create_html_preview(soup, elements, parsed_query['selector'])
            
            return {
                'matched_elements': len(elements),
                'selector': parsed_query['selector'],
                'parsed_query': parsed_query,
                'html_preview': html_preview,
                'sample_elements': [str(elem)[:200] + '...' if len(str(elem)) > 200 else str(elem) 
                                  for elem in elements[:3]]
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'matched_elements': 0,
                'selector': None,
                'html_preview': None
            }
    
    def _create_html_preview(self, soup, matched_elements, selector):
        """إنشاء معاينة HTML مع تمييز العناصر المطابقة"""
        try:
            # أخذ جزء من HTML
            body = soup.find('body')
            if not body:
                body = soup
            
            html_str = str(body)[:2000]  # أول 2000 حرف
            
            # تمييز العناصر المطابقة
            for element in matched_elements[:5]:  # أول 5 عناصر فقط
                element_str = str(element)[:100]
                if element_str in html_str:
                    html_str = html_str.replace(
                        element_str, 
                        f'<span class="highlight">{element_str}</span>'
                    )
            
            return html_str
            
        except Exception:
            return "خطأ في إنشاء معاينة HTML"


class DataExporter:
    """كلاس لتصدير البيانات"""
    
    def __init__(self, export_folder='exports'):
        self.export_folder = export_folder
        if not os.path.exists(export_folder):
            os.makedirs(export_folder)
    
    def export_to_json(self, results, max_items_per_file=100, site_name=None):
        """تصدير البيانات إلى ملفات JSON منفصلة للأفلام والمسلسلات"""
        if not results:
            return []

        files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # فصل الأفلام والمسلسلات
        movies = [item for item in results if item.get('type') == 'movie']
        series = [item for item in results if item.get('type') == 'series']

        print(f"📊 إجمالي البيانات: {len(results)} عنصر")
        print(f"🎬 أفلام: {len(movies)}")
        print(f"📺 مسلسلات: {len(series)}")

        # تصدير الأفلام
        if movies:
            movie_files = self._export_movies(movies, max_items_per_file, timestamp, site_name)
            files.extend(movie_files)

        # تصدير المسلسلات
        if series:
            series_files = self._export_series(series, max_items_per_file, timestamp, site_name)
            files.extend(series_files)

        return files

    def export_to_single_file(self, results, format_type='json', site_name=None):
        """تصدير جميع البيانات في ملف واحد مجمع"""
        if not results:
            return []

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format_type == 'json':
            return self._export_single_json(results, timestamp, site_name)
        elif format_type == 'csv':
            return self._export_single_csv(results, timestamp, site_name)
        elif format_type == 'xlsx':
            return self._export_single_xlsx(results, timestamp, site_name)
        else:
            return self._export_single_json(results, timestamp, site_name)

    def export_to_separate_files(self, results, max_items_per_file, format_type='json', site_name=None):
        """تصدير البيانات في ملفات منفصلة للأفلام والمسلسلات"""
        if not results:
            return []

        files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # فصل الأفلام والمسلسلات
        movies = [item for item in results if item.get('type') == 'movie']
        series = [item for item in results if item.get('type') == 'series']

        if format_type == 'json':
            if movies:
                movie_files = self._export_movies(movies, max_items_per_file, timestamp, site_name)
                files.extend(movie_files)
            if series:
                series_files = self._export_series(series, max_items_per_file, timestamp, site_name)
                files.extend(series_files)
        # يمكن إضافة دعم CSV و XLSX لاحقاً

        return files

    def _export_single_json(self, results, timestamp, site_name=None):
        """تصدير جميع البيانات في ملف JSON واحد"""
        site_prefix = f"{site_name}_" if site_name else ""
        filename = f"{site_prefix}جميع_البيانات_{len(results)}عنصر_{timestamp}.json"

        # فصل الأفلام والمسلسلات
        movies = [item for item in results if item.get('type') == 'movie']
        series = [item for item in results if item.get('type') == 'series']

        formatted_data = {
            "export_info": {
                "timestamp": timestamp,
                "total_items": len(results),
                "movies_count": len(movies),
                "series_count": len(series)
            },
            "movies_info": [
                {
                    "movies_name": item.get('movies_name') or item.get('title', ''),
                    "movies_img": item.get('movies_img') or item.get('imageUrl') or item.get('image', ''),
                    "movies_href": item.get('movies_href') or item.get('link', '')
                }
                for item in movies
            ],
            "series_info": [
                {
                    "series_name": item.get('series_name') or item.get('title', ''),
                    "series_img": item.get('series_img') or item.get('imageUrl') or item.get('image', ''),
                    "series_href": item.get('series_href') or item.get('link', '')
                }
                for item in series
            ]
        }

        filepath = os.path.join(self.export_folder, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(formatted_data, f, ensure_ascii=False, indent=2)

        site_info = f" من {site_name}" if site_name else ""
        print(f"✅ تم تصدير {len(results)} عنصر{site_info} إلى ملف واحد: {filename}")
        return [filename]

    def _export_single_csv(self, results, timestamp, site_name=None):
        """تصدير البيانات إلى ملف CSV واحد"""
        try:
            import csv
            site_prefix = f"{site_name}_" if site_name else ""
            filename = f"{site_prefix}جميع_البيانات_{len(results)}عنصر_{timestamp}.csv"
            filepath = os.path.join(self.export_folder, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['type', 'name', 'image', 'link']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for item in results:
                    writer.writerow({
                        'type': 'فيلم' if item.get('type') == 'movie' else 'مسلسل',
                        'name': item.get('title', ''),
                        'image': item.get('imageUrl') or item.get('image', ''),
                        'link': item.get('link', '')
                    })

            site_info = f" من {site_name}" if site_name else ""
            print(f"✅ تم تصدير {len(results)} عنصر{site_info} إلى CSV: {filename}")
            return [filename]
        except ImportError:
            print("❌ مكتبة CSV غير متوفرة")
            return self._export_single_json(results, timestamp, site_name)

    def _export_single_xlsx(self, results, timestamp, site_name=None):
        """تصدير البيانات إلى ملف Excel"""
        try:
            import openpyxl
            from openpyxl import Workbook

            site_prefix = f"{site_name}_" if site_name else ""
            filename = f"{site_prefix}جميع_البيانات_{len(results)}عنصر_{timestamp}.xlsx"
            filepath = os.path.join(self.export_folder, filename)

            wb = Workbook()

            # ورقة الأفلام
            movies = [item for item in results if item.get('type') == 'movie']
            if movies:
                ws_movies = wb.active
                ws_movies.title = "الأفلام"
                ws_movies.append(['اسم الفيلم', 'رابط الصورة', 'رابط الفيلم'])

                for item in movies:
                    ws_movies.append([
                        item.get('title', ''),
                        item.get('imageUrl') or item.get('image', ''),
                        item.get('link', '')
                    ])

            # ورقة المسلسلات
            series = [item for item in results if item.get('type') == 'series']
            if series:
                ws_series = wb.create_sheet("المسلسلات")
                ws_series.append(['اسم المسلسل', 'رابط الصورة', 'رابط المسلسل'])

                for item in series:
                    ws_series.append([
                        item.get('title', ''),
                        item.get('imageUrl') or item.get('image', ''),
                        item.get('link', '')
                    ])

            wb.save(filepath)
            site_info = f" من {site_name}" if site_name else ""
            print(f"✅ تم تصدير {len(results)} عنصر{site_info} إلى Excel: {filename}")
            return [filename]

        except ImportError:
            print("❌ مكتبة openpyxl غير متوفرة")
            return self._export_single_json(results, timestamp, site_name)

    def _export_movies(self, movies, max_items_per_file, timestamp, site_name=None):
        """تصدير الأفلام إلى ملفات منفصلة"""
        files = []
        site_prefix = f"{site_name}_" if site_name else ""

        for i in range(0, len(movies), max_items_per_file):
            chunk = movies[i:i + max_items_per_file]
            file_number = (i // max_items_per_file) + 1

            if len(movies) > max_items_per_file:
                filename = f"{site_prefix}أفلام_{len(chunk)}عنصر_{timestamp}_part{file_number}.json"
            else:
                filename = f"{site_prefix}أفلام_{len(chunk)}عنصر_{timestamp}.json"

            # تنسيق البيانات حسب المطلوب
            formatted_data = {
                "movies_info": [
                    {
                        "movies_name": item.get('movies_name') or item.get('title', ''),
                        "movies_img": item.get('movies_img') or item.get('imageUrl') or item.get('image', ''),
                        "movies_href": item.get('movies_href') or item.get('link', '')
                    }
                    for item in chunk
                ]
            }

            filepath = os.path.join(self.export_folder, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(formatted_data, f, ensure_ascii=False, indent=2)

            files.append(filename)
            site_info = f" من {site_name}" if site_name else ""
            print(f"✅ تم تصدير {len(chunk)} فيلم{site_info} إلى: {filename}")

        return files

    def _export_series(self, series, max_items_per_file, timestamp, site_name=None):
        """تصدير المسلسلات إلى ملفات منفصلة"""
        files = []
        site_prefix = f"{site_name}_" if site_name else ""

        for i in range(0, len(series), max_items_per_file):
            chunk = series[i:i + max_items_per_file]
            file_number = (i // max_items_per_file) + 1

            if len(series) > max_items_per_file:
                filename = f"{site_prefix}مسلسلات_{len(chunk)}عنصر_{timestamp}_part{file_number}.json"
            else:
                filename = f"{site_prefix}مسلسلات_{len(chunk)}عنصر_{timestamp}.json"

            # تنسيق البيانات حسب المطلوب
            formatted_data = {
                "series_info": [
                    {
                        "series_name": item.get('series_name') or item.get('title', ''),
                        "series_img": item.get('series_img') or item.get('imageUrl') or item.get('image', ''),
                        "series_href": item.get('series_href') or item.get('link', '')
                    }
                    for item in chunk
                ]
            }

            filepath = os.path.join(self.export_folder, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(formatted_data, f, ensure_ascii=False, indent=2)

            files.append(filename)
            site_info = f" من {site_name}" if site_name else ""
            print(f"✅ تم تصدير {len(chunk)} مسلسل{site_info} إلى: {filename}")

        return files

    # دوال الاستخراج المتحكم بها
    def scrape_website_controlled(self, url, query, max_pages=1, page_load_delay=2, control_callback=None):
        """استخراج البيانات من موقع واحد مع التحكم في العملية"""
        try:
            print(f"🚀 بدء الاستخراج المتحكم به من: {url}")

            # فحص حالة التحكم قبل البدء
            if control_callback:
                control = control_callback()
                if control.get('should_stop'):
                    print("⏹️ تم إيقاف العملية قبل البدء")
                    return []

            all_results = []

            for page_num in range(1, max_pages + 1):
                # فحص حالة التحكم قبل كل صفحة
                if control_callback:
                    control = control_callback()
                    if control.get('should_stop'):
                        print(f"⏹️ تم إيقاف العملية في الصفحة {page_num}")
                        break

                    # انتظار أثناء الإيقاف المؤقت
                    while control.get('is_paused') and not control.get('should_stop'):
                        print(f"⏸️ العملية متوقفة مؤقتاً في الصفحة {page_num}")
                        time.sleep(1)
                        control = control_callback()

                print(f"📄 معالجة الصفحة {page_num} من {max_pages}")

                # تحديد URL للصفحة الحالية
                if page_num == 1:
                    current_url = url
                else:
                    current_url = f"{url}?page={page_num}"

                try:
                    # تحميل الصفحة مع التأخير
                    print(f"⏳ انتظار {page_load_delay} ثانية قبل تحميل الصفحة...")
                    time.sleep(page_load_delay)

                    # فحص حالة التحكم بعد التأخير
                    if control_callback:
                        control = control_callback()
                        if control.get('should_stop'):
                            print("⏹️ تم إيقاف العملية أثناء التأخير")
                            break

                    response = self.session.get(current_url, timeout=30)
                    response.raise_for_status()

                    # استخراج البيانات
                    page_results = self.extract_data_from_html(response.text, query)

                    if page_results:
                        all_results.extend(page_results)
                        print(f"✅ تم استخراج {len(page_results)} عنصر من الصفحة {page_num}")

                        # تحديث النتائج الحالية في التحكم
                        if control_callback:
                            control = control_callback()
                            control['current_results'] = all_results
                    else:
                        print(f"⚠️ لم يتم العثور على نتائج في الصفحة {page_num}")

                except Exception as e:
                    print(f"❌ خطأ في معالجة الصفحة {page_num}: {str(e)}")
                    continue

            print(f"✅ اكتمل الاستخراج المتحكم به: {len(all_results)} عنصر")
            return all_results

        except Exception as e:
            print(f"❌ خطأ في الاستخراج المتحكم به: {str(e)}")
            return []

    def scrape_multiple_pages_controlled(self, pattern_info, query, max_pages, start_page=1, page_load_delay=2, control_callback=None):
        """استخراج البيانات من صفحات متتالية مع التحكم في العملية"""
        try:
            print(f"🚀 بدء الاستخراج المتتالي المتحكم به من {max_pages} صفحة")
            print(f"📊 النمط: {pattern_info['pattern']}")
            print(f"🔢 البدء من الصفحة: {start_page}")

            # فحص حالة التحكم قبل البدء
            if control_callback:
                control = control_callback()
                if control.get('should_stop'):
                    print("⏹️ تم إيقاف العملية قبل البدء")
                    return [], {'movies_count': 0, 'series_count': 0, 'total_count': 0}

            all_results = []
            movies_count = 0
            series_count = 0

            for page_num in range(start_page, start_page + max_pages):
                # فحص حالة التحكم قبل كل صفحة
                if control_callback:
                    control = control_callback()
                    if control.get('should_stop'):
                        print(f"⏹️ تم إيقاف العملية في الصفحة {page_num}")
                        break

                    # انتظار أثناء الإيقاف المؤقت
                    while control.get('is_paused') and not control.get('should_stop'):
                        print(f"⏸️ العملية متوقفة مؤقتاً في الصفحة {page_num}")
                        time.sleep(1)
                        control = control_callback()

                print(f"📄 معالجة الصفحة {page_num} من {start_page + max_pages - 1}")

                # إنشاء URL للصفحة الحالية
                current_url = pattern_info['pattern'].replace(pattern_info['variable'], str(page_num))

                try:
                    # تحميل الصفحة مع التأخير
                    print(f"⏳ انتظار {page_load_delay} ثانية قبل تحميل الصفحة...")
                    time.sleep(page_load_delay)

                    # فحص حالة التحكم بعد التأخير
                    if control_callback:
                        control = control_callback()
                        if control.get('should_stop'):
                            print("⏹️ تم إيقاف العملية أثناء التأخير")
                            break

                    response = self.session.get(current_url, timeout=30)
                    response.raise_for_status()

                    # استخراج البيانات
                    page_results = self.extract_data_from_html(response.text, query)

                    if page_results:
                        all_results.extend(page_results)

                        # عد الأفلام والمسلسلات
                        page_movies = len([item for item in page_results if item.get('type') == 'movie'])
                        page_series = len([item for item in page_results if item.get('type') == 'series'])

                        movies_count += page_movies
                        series_count += page_series

                        print(f"✅ تم استخراج {len(page_results)} عنصر من الصفحة {page_num}")
                        print(f"   🎬 أفلام: {page_movies} | 📺 مسلسلات: {page_series}")

                        # تحديث النتائج الحالية في التحكم
                        if control_callback:
                            control = control_callback()
                            control['current_results'] = all_results
                    else:
                        print(f"⚠️ لم يتم العثور على نتائج في الصفحة {page_num}")

                except Exception as e:
                    print(f"❌ خطأ في معالجة الصفحة {page_num}: {str(e)}")
                    continue

            summary = {
                'movies_count': movies_count,
                'series_count': series_count,
                'total_count': len(all_results),
                'pages_processed': min(max_pages, len(all_results) // 10 + 1)
            }

            print(f"✅ اكتمل الاستخراج المتتالي المتحكم به:")
            print(f"   📊 إجمالي العناصر: {len(all_results)}")
            print(f"   🎬 أفلام: {movies_count}")
            print(f"   📺 مسلسلات: {series_count}")

            return all_results, summary

        except Exception as e:
            print(f"❌ خطأ في الاستخراج المتتالي المتحكم به: {str(e)}")
            return [], {'movies_count': 0, 'series_count': 0, 'total_count': 0}
