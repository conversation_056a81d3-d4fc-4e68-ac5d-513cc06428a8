#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محسن لاستراتيجية aflam.top مع التركيز على استخراج الصور
"""

import sys
import os
import json
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraper_classes import WebScraper

def test_aflam_image_extraction():
    """اختبار استخراج الصور من موقع aflam.top"""
    
    print("🚀 بدء اختبار استخراج الصور من aflam.top")
    print("=" * 60)
    
    # إنشاء scraper
    scraper = WebScraper()
    
    # تعيين استراتيجية aflam.top
    print("🔧 تعيين استراتيجية aflam.top...")
    scraper.set_strategy(3)
    
    # عرض معلومات الاستراتيجية
    strategy_info = scraper.get_current_strategy_mode()
    print(f"📋 الاستراتيجية المحددة: {strategy_info}")
    
    # الروابط للاختبار
    test_urls = [
        "https://aflam.top/category/%d8%a7%d9%81%d9%84%d8%a7%d9%85/%d8%a7%d9%81%d9%84%d8%a7%d9%85-%d8%a7%d8%ac%d9%86%d8%a8%d9%8a%d8%a9/",
        "https://aflam.top/genre/%d8%a7%d9%83%d8%b4%d9%86/?page=2"
    ]
    
    all_results = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🔍 اختبار {i}: {url}")
        print("-" * 50)
        
        try:
            # تشغيل الاستخراج
            results = scraper.scrape_website(url, "أفلام")
            
            if results:
                print(f"✅ تم استخراج {len(results)} عنصر")
                
                # تحليل الصور
                images_found = 0
                valid_images = 0
                default_images = 0
                
                for j, item in enumerate(results[:10]):  # فحص أول 10 عناصر
                    print(f"\n📋 العنصر {j+1}:")
                    print(f"   العنوان: {item.get('title', 'غير متوفر')[:60]}")
                    print(f"   الرابط: {item.get('link', 'غير متوفر')[:80]}")
                    
                    image = item.get('image', 'غير متوفر')
                    if image and image != 'غير متوفر':
                        images_found += 1
                        print(f"   🖼️ الصورة: {image[:100]}")
                        
                        # فحص نوع الصورة
                        image_lower = image.lower()
                        if any(default in image_lower for default in ['default', 'placeholder', 'logo', 'blank']):
                            default_images += 1
                            print("   ⚠️ صورة افتراضية محتملة")
                        else:
                            valid_images += 1
                            print("   ✅ صورة صالحة")
                    else:
                        print("   ❌ لا توجد صورة")
                    
                    print(f"   النوع: {item.get('type', 'غير محدد')}")
                
                # إحصائيات الصور
                print(f"\n📊 إحصائيات الصور:")
                print(f"   - إجمالي العناصر المفحوصة: {min(10, len(results))}")
                print(f"   - عناصر بصور: {images_found}")
                print(f"   - صور صالحة: {valid_images}")
                print(f"   - صور افتراضية: {default_images}")
                print(f"   - معدل نجاح الصور: {(valid_images/min(10, len(results)))*100:.1f}%")
                
                all_results.extend(results)
                
            else:
                print("❌ لم يتم استخراج أي نتائج")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
    
    # حفظ النتائج
    if all_results:
        output_file = f"aflam_images_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # إعداد البيانات للحفظ
        save_data = {
            'timestamp': datetime.now().isoformat(),
            'test_type': 'aflam_images_extraction',
            'total_results': len(all_results),
            'results': all_results[:50]  # حفظ أول 50 نتيجة
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            print(f"\n💾 تم حفظ النتائج في: {output_file}")
        except Exception as e:
            print(f"❌ خطأ في حفظ النتائج: {str(e)}")
    
    print(f"\n✅ اكتمل اختبار استخراج الصور!")
    print(f"📊 إجمالي النتائج: {len(all_results)}")

def test_image_validation():
    """اختبار دالة التحقق من صحة الصور"""
    
    print("\n🧪 اختبار دالة التحقق من صحة الصور")
    print("=" * 50)
    
    from scraper_classes import AflamTopStrategy
    
    strategy = AflamTopStrategy()
    
    # صور للاختبار
    test_images = [
        "https://aflam.top/wp-content/uploads/2023/movie-poster.jpg",  # صورة صالحة
        "https://aflam.top/default.jpg",  # صورة افتراضية
        "https://aflam.top/logo.png",  # شعار الموقع
        "https://aflam.top/placeholder.jpg",  # صورة مؤقتة
        "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",  # صورة data
        "https://aflam.top/wp-content/uploads/2023/action-movie-1080p.jpg",  # صورة صالحة
    ]
    
    for i, image_url in enumerate(test_images, 1):
        print(f"\n🔍 اختبار الصورة {i}:")
        print(f"   الرابط: {image_url}")
        
        # محاكاة عنصر HTML
        class MockElement:
            def __init__(self, src):
                self.src = src
                self.name = 'img'
            
            def get(self, attr):
                if attr == 'src':
                    return self.src
                return None
        
        mock_element = MockElement(image_url)
        result = strategy._find_aflam_image(mock_element, "https://aflam.top")
        
        if result:
            print(f"   ✅ نتيجة: {result}")
        else:
            print(f"   ❌ تم رفض الصورة")

if __name__ == "__main__":
    try:
        test_aflam_image_extraction()
        test_image_validation()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
