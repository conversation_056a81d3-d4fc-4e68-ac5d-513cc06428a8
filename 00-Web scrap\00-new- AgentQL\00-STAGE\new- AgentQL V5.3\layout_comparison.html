<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقارنة التخطيط - قبل وبعد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow-lg">
                    <div class="card-header bg-info text-white text-center">
                        <h2><i class="fas fa-compare"></i> مقارنة التخطيط - قبل وبعد التحسين</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- التخطيط القديم -->
                        <div class="row mb-5">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <h4><i class="fas fa-history"></i> التخطيط القديم (عمودي)</h4>
                                    <p>الكلمات الافتراضية كانت تأخذ مساحة أكبر مع تخطيط عمودي</p>
                                </div>
                                
                                <label class="form-label"><i class="fas fa-tags"></i> الكلمات الافتراضية (التخطيط القديم):</label>
                                <div class="default-keywords-container">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="old-keyword-film" value="فيلم">
                                        <label class="form-check-label" for="old-keyword-film">
                                            <span class="badge bg-primary">فيلم</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="old-keyword-watch" value="مشاهدة">
                                        <label class="form-check-label" for="old-keyword-watch">
                                            <span class="badge bg-success">مشاهدة</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="old-keyword-movie" value="movie">
                                        <label class="form-check-label" for="old-keyword-movie">
                                            <span class="badge bg-info">movie</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="old-keyword-series" value="مسلسل">
                                        <label class="form-check-label" for="old-keyword-series">
                                            <span class="badge bg-warning">مسلسل</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="old-keyword-episode" value="حلقة">
                                        <label class="form-check-label" for="old-keyword-episode">
                                            <span class="badge bg-secondary">حلقة</span>
                                        </label>
                                    </div>
                                </div>
                                <small class="form-text text-muted">❌ يأخذ مساحة أكبر ويبدو مبعثراً</small>
                            </div>
                        </div>

                        <hr class="my-5">

                        <!-- التخطيط الجديد -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-success">
                                    <h4><i class="fas fa-sparkles"></i> التخطيط الجديد (أفقي مضغوط)</h4>
                                    <p>الكلمات الافتراضية في صف أفقي واحد لتوفير المساحة</p>
                                </div>
                                
                                <label class="form-label"><i class="fas fa-tags"></i> الكلمات الافتراضية (التخطيط الجديد):</label>
                                <div class="default-keywords-container-horizontal">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="new-keyword-film" value="فيلم" checked>
                                        <label class="form-check-label" for="new-keyword-film">
                                            <span class="badge bg-primary">فيلم</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="new-keyword-watch" value="مشاهدة" checked>
                                        <label class="form-check-label" for="new-keyword-watch">
                                            <span class="badge bg-success">مشاهدة</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="new-keyword-movie" value="movie">
                                        <label class="form-check-label" for="new-keyword-movie">
                                            <span class="badge bg-info">movie</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="new-keyword-series" value="مسلسل">
                                        <label class="form-check-label" for="new-keyword-series">
                                            <span class="badge bg-warning">مسلسل</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="new-keyword-episode" value="حلقة">
                                        <label class="form-check-label" for="new-keyword-episode">
                                            <span class="badge bg-secondary">حلقة</span>
                                        </label>
                                    </div>
                                </div>
                                <small class="form-text text-muted">✅ مضغوط، منظم، ويوفر المساحة</small>
                            </div>
                        </div>

                        <!-- مقارنة المميزات -->
                        <div class="row mt-5">
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5><i class="fas fa-history"></i> التخطيط القديم</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-times text-danger"></i> يأخذ مساحة أكبر</li>
                                            <li><i class="fas fa-times text-danger"></i> تخطيط عمودي مبعثر</li>
                                            <li><i class="fas fa-times text-danger"></i> مسافات كبيرة بين العناصر</li>
                                            <li><i class="fas fa-check text-success"></i> سهل القراءة</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5><i class="fas fa-sparkles"></i> التخطيط الجديد</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> يوفر المساحة</li>
                                            <li><i class="fas fa-check text-success"></i> تخطيط أفقي منظم</li>
                                            <li><i class="fas fa-check text-success"></i> مسافات محسنة</li>
                                            <li><i class="fas fa-check text-success"></i> سهل القراءة</li>
                                            <li><i class="fas fa-check text-success"></i> متجاوب مع الشاشات الصغيرة</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إضافة تأثيرات تفاعلية لجميع الكلمات الافتراضية
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
        allCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const label = this.nextElementSibling;
                const badge = label.querySelector('.badge');
                
                if (this.checked) {
                    badge.style.transform = 'scale(1.1)';
                    badge.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                } else {
                    badge.style.transform = 'scale(1)';
                    badge.style.boxShadow = 'none';
                }
            });
        });

        console.log('✅ تم تحميل صفحة مقارنة التخطيط');
        console.log('📊 يمكنك رؤية الفرق بين التخطيط القديم والجديد');
    </script>
</body>
</html>
