#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from scraper_classes import WebScraper
import sys

def test_website_extraction(url, site_name):
    """اختبار استخراج البيانات من موقع"""
    print(f"🧪 اختبار {site_name}...")
    print(f"🌐 الرابط: {url}")
    print("=" * 60)
    
    try:
        scraper = WebScraper()
        results = scraper.scrape_website(url, 'movies', 1, 1)
        
        print(f"✅ النتائج: {len(results)} عنصر")
        
        if results:
            print("\n🎬 أول 5 نتائج:")
            for i, result in enumerate(results[:5]):
                print(f"\n--- نتيجة {i+1} ---")
                print(f"العنوان: {result.get('title', 'لا يوجد')}")
                print(f"الرابط: {result.get('link', 'لا يوجد')}")
                print(f"الصورة: {result.get('image', 'لا يوجد')}")
                print(f"النوع: {result.get('type', 'لا يوجد')}")
        else:
            print("❌ لا توجد نتائج")
            
        return len(results)
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return 0

def main():
    """الدالة الرئيسية"""
    test_sites = [
        ("https://a.asd.homes/category/foreign-movies-6/page/2/", "موقع ASD Homes"),
        ("https://mc.turkishasq.com/category.php?cat=movies", "موقع Turkish ASQ"),
        ("https://vid.shahidwbas.tv/categories.php?cat=adult-movies&page=8&order=DESC", "موقع شاهد وبس")
    ]
    
    total_results = 0
    
    for url, site_name in test_sites:
        results_count = test_website_extraction(url, site_name)
        total_results += results_count
        print("\n" + "=" * 80 + "\n")
    
    print(f"📊 إجمالي النتائج من جميع المواقع: {total_results}")
    
    if total_results > 0:
        print("✅ التطبيق يعمل بنجاح!")
    else:
        print("❌ التطبيق يحتاج إلى مزيد من التحسينات")

if __name__ == "__main__":
    main()
