<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة استخراج بيانات الأفلام - Movie Data Scraper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-film"></i> أداة استخراج بيانات الأفلام من المواقع</h1>
            <p>استخرج اسم الفيلم، رابط الفيلم، ورابط صورة الفيلم من أي موقع ويب</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Alerts Container -->
            <div id="alerts"></div>

            <!-- Loading Indicator -->
            <div id="loading" class="loading" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري المعالجة...</p>
            </div>

            <!-- Scraping Mode Selection -->
            <div class="form-section">
                <h3><i class="fas fa-cogs"></i> وضع الاستخراج</h3>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="scraping-mode-selector">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="scrapingMode" id="singlePageMode" value="single">
                                    <label class="form-check-label" for="singlePageMode">
                                        <i class="fas fa-file"></i> صفحة منفردة
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="scrapingMode" id="multiPageMode" value="multi" checked>
                                    <label class="form-check-label" for="multiPageMode">
                                        <i class="fas fa-files"></i> صفحات متتالية
                                    </label>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                اختر وضع الاستخراج: صفحة واحدة أو عدة صفحات متتالية
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Multi-Page Settings Section -->
            <div class="form-section" id="multiPageSection">
                <h3><i class="fas fa-file-alt"></i> إعدادات الصفحات المتتالية</h3>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>كيفية الاستخدام:</strong> أدخل 3 صفحات متتالية لاستنتاج النمط تلقائياً، ثم حدد عدد الصفحات المطلوب استخراجها.
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="page1">الصفحة الأولى: <span class="text-danger">*</span></label>
                            <input type="url" id="page1" class="form-control" placeholder="https://example.com/page/1">
                            <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="page2">الصفحة الثانية: <span class="text-danger">*</span></label>
                            <input type="url" id="page2" class="form-control" placeholder="https://example.com/page/2">
                            <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="page3">الصفحة الثالثة: <span class="text-danger">*</span></label>
                            <input type="url" id="page3" class="form-control" placeholder="https://example.com/page/3">
                            <small class="form-text text-muted">مطلوبة لاستنتاج النمط</small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="maxPages">عدد الصفحات المستهدفة:</label>
                            <input type="number" id="maxPages" class="form-control" value="5" min="1" max="100">
                            <small class="form-text text-muted">العدد الإجمالي للصفحات المراد استخراجها</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="startFromPage">البدء من الصفحة:</label>
                            <input type="number" id="startFromPage" class="form-control" value="1" min="1">
                            <small class="form-text text-muted">رقم الصفحة للبدء منها</small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <button id="analyzeBtn" class="btn btn-warning">
                            <i class="fas fa-chart-line"></i> تحليل نمط الصفحات
                        </button>
                        <div id="patternInfo" style="display: none; margin-top: 15px;"></div>
                    </div>
                </div>
            </div>

            <!-- URL Input Section -->
            <div class="form-section" id="singlePageSection" style="display: none;">
                <h3><i class="fas fa-link"></i> رابط موقع الأفلام</h3>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="url">رابط الموقع المطلوب استخراج الأفلام منه:</label>
                            <input type="url" id="url" class="form-control" placeholder="https://movies-site.com/movies">
                            <small class="form-text text-muted">
                                أدخل رابط الصفحة التي تحتوي على قائمة الأفلام
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Query Section -->
            <div class="form-section collapsible-section">
                <div class="collapsible-header" onclick="toggleSection('querySection')">
                    <h3><i class="fas fa-search"></i> استعلام البحث عن الأفلام</h3>
                    <i class="fas fa-chevron-down toggle-icon" id="querySection-icon"></i>
                </div>
                <div class="collapsible-content" id="querySection-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="query">استعلام البحث (مُعد مسبقاً للأفلام):</label>
                                <textarea id="query" class="form-control" rows="3"
                                    placeholder="SELECT title, link, image FROM page WHERE class='movie-card'">SELECT title, link, image FROM page</textarea>
                                <small class="form-text text-muted">
                                    <strong>🎬 الاستعلام الافتراضي يبحث عن:</strong><br>
                                    • <strong>title</strong> = اسم الفيلم<br>
                                    • <strong>link</strong> = رابط الفيلم<br>
                                    • <strong>image</strong> = رابط صورة الفيلم
                                </small>
                            </div>
                            <div id="queryPreview" style="display: none;" class="alert alert-info"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters Section -->
            <div class="form-section collapsible-section">
                <div class="collapsible-header" onclick="toggleSection('filtersSection')">
                    <h3><i class="fas fa-filter"></i> الفلاتر</h3>
                    <i class="fas fa-chevron-down toggle-icon" id="filtersSection-icon"></i>
                </div>
                <div class="collapsible-content" id="filtersSection-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="maxItemsPerFile">عدد العناصر لكل ملف:</label>
                                <input type="number" id="maxItemsPerFile" class="form-control" value="4000" min="1" max="10000">
                                <small class="form-text text-muted">سيتم فصل الأفلام والمسلسلات تلقائياً (افتراضي: 4000 عنصر)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="pageLoadDelay">⏱️ تأخير تحميل الصفحة (ثانية):</label>
                                <input type="number" id="pageLoadDelay" class="form-control" value="2" min="0" max="10" step="0.5">
                                <small class="form-text text-muted">وقت الانتظار بعد تحميل الصفحة للمحتوى الديناميكي</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Action Buttons -->
            <div class="text-center">
                <button id="scrapeBtn" class="btn btn-primary btn-lg">
                    <i class="fas fa-play"></i> بدء الاستخراج
                </button>
                <button id="debugBtn" class="btn btn-warning btn-lg">
                    <i class="fas fa-bug"></i> تصحيح الاستعلام
                </button>
                <div class="btn-group" role="group">
                    <button id="exportBtn" type="button" class="btn btn-success btn-lg dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download"></i> تصدير النتائج
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" id="exportSingleFile">
                            <i class="fas fa-file"></i> ملف مجمع واحد
                        </a></li>
                        <li><a class="dropdown-item" href="#" id="exportMultipleFiles">
                            <i class="fas fa-files"></i> ملفات متعددة
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" id="exportSettings">
                            <i class="fas fa-cog"></i> إعدادات التصدير
                        </a></li>
                    </ul>
                </div>
            </div>

            <!-- Export Settings Modal -->
            <div class="modal fade" id="exportSettingsModal" tabindex="-1" aria-labelledby="exportSettingsModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exportSettingsModalLabel">
                                <i class="fas fa-cog"></i> إعدادات التصدير
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group mb-3">
                                <label for="exportType">نوع التصدير:</label>
                                <select id="exportType" class="form-control">
                                    <option value="single">ملف واحد مجمع</option>
                                    <option value="multiple">ملفات متعددة</option>
                                    <option value="separate">ملفات منفصلة (أفلام ومسلسلات)</option>
                                </select>
                            </div>
                            <div class="form-group mb-3" id="itemsPerFileGroup">
                                <label for="exportItemsPerFile">عدد العناصر لكل ملف:</label>
                                <input type="number" id="exportItemsPerFile" class="form-control" value="4000" min="1" max="10000">
                                <small class="form-text text-muted">يطبق فقط عند اختيار ملفات متعددة (افتراضي: 4000 عنصر)</small>
                            </div>
                            <div class="form-group mb-3">
                                <label for="exportFormat">تنسيق الملف:</label>
                                <select id="exportFormat" class="form-control">
                                    <option value="json">JSON</option>
                                    <option value="csv">CSV</option>
                                    <option value="xlsx">Excel (XLSX)</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="exportPath">📂 مجلد التصدير (اختياري):</label>
                                <div class="input-group">
                                    <input type="text" id="exportPath" class="form-control" placeholder="اختر مجلد التصدير" readonly>
                                    <button class="btn btn-outline-secondary" type="button" id="selectFolderBtn">
                                        <i class="fas fa-folder-open"></i> اختيار مجلد
                                    </button>
                                </div>
                                <small class="form-text text-muted">إذا لم تختر مجلد، سيتم التحميل في مجلد التحميلات الافتراضي</small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="applyExportSettings">تطبيق وتصدير</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Progress Log Section -->
            <div class="form-section">
                <h3><i class="fas fa-list-alt"></i> سجل التطور اللحظي</h3>

                <!-- Progress Stats -->
                <div class="progress-stats" id="progressStats" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="moviesCount">0</div>
                            <div class="stat-label">🎬 أفلام</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="seriesCount">0</div>
                            <div class="stat-label">📺 مسلسلات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalCount">0</div>
                            <div class="stat-label">📊 إجمالي</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="currentPage">0</div>
                            <div class="stat-label">📄 الصفحة الحالية</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progressPercentage">0%</span>
                            <span id="progressStatus">جاهز للبدء</span>
                        </div>
                    </div>
                </div>

                <div class="progress-log-container">
                    <div id="progressLog" class="progress-log">
                        <div class="log-entry info">
                            <span class="timestamp">[جاهز]</span>
                            <span class="message">🚀 التطبيق جاهز للاستخدام</span>
                        </div>
                    </div>
                    <div class="log-controls">
                        <button id="clearLogBtn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash"></i> مسح السجل
                        </button>
                        <button id="scrollToBottomBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-down"></i> انتقال للأسفل
                        </button>
                        <button id="pauseScrapingBtn" class="btn btn-sm btn-outline-warning" style="display: none;">
                            <i class="fas fa-pause"></i> إيقاف مؤقت
                        </button>
                        <button id="resumeScrapingBtn" class="btn btn-sm btn-outline-success" style="display: none;">
                            <i class="fas fa-play"></i> استئناف العملية
                        </button>
                        <button id="stopAndExportBtn" class="btn btn-sm btn-outline-danger" style="display: none;">
                            <i class="fas fa-stop-circle"></i> إيقاف وتصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="results-section" style="display: none;">
                <h3><i class="fas fa-table"></i> النتائج</h3>

                <!-- Results Filter Section -->
                <div class="results-filter-section mb-4">
                    <h4><i class="fas fa-filter"></i> فلترة وتصفية النتائج</h4>

                    <!-- Filter by Name -->
                    <div class="filter-card mb-3">
                        <h5><i class="fas fa-text-width"></i> التصفية بالاسم</h5>

                        <!-- Default Keywords Section -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label"><i class="fas fa-tags"></i> الكلمات الافتراضية:</label>
                                <div class="default-keywords-container-horizontal">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-film" value="فيلم">
                                        <label class="form-check-label" for="keyword-film">
                                            <span class="badge bg-primary">فيلم</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-watch" value="مشاهدة">
                                        <label class="form-check-label" for="keyword-watch">
                                            <span class="badge bg-success">مشاهدة</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-movie" value="movie">
                                        <label class="form-check-label" for="keyword-movie">
                                            <span class="badge bg-info">movie</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-series" value="مسلسل">
                                        <label class="form-check-label" for="keyword-series">
                                            <span class="badge bg-warning">مسلسل</span>
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-episode" value="حلقة">
                                        <label class="form-check-label" for="keyword-episode">
                                            <span class="badge bg-secondary">حلقة</span>
                                        </label>
                                    </div>
                                </div>
                                <small class="form-text text-muted">اختر الكلمات الافتراضية التي تريد البحث عنها</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="filterSearchTerm">كلمات البحث الإضافية:</label>
                                    <input type="text" id="filterSearchTerm" class="form-control" placeholder="أدخل كلمات إضافية للبحث (مفصولة بفاصلة)">
                                    <small class="form-text text-muted">مثال: أكشن, كوميديا, رعب</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>إجراءات التصفية بالاسم:</label>
                                    <div class="btn-group-vertical d-grid gap-2">
                                        <button id="excludeByNameBtn" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-minus-circle"></i> استبعاد بالاسم
                                        </button>
                                        <button id="includeByNameBtn" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-plus-circle"></i> اختيار بالاسم
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter by Image -->
                    <div class="filter-card mb-3">
                        <h5><i class="fas fa-image"></i> التصفية بالصور</h5>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label>خيارات التصفية بالصور:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="excludeNoImageItems">
                                        <label class="form-check-label" for="excludeNoImageItems">
                                            استبعاد العناصر التي لا توجد لها صورة
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">سيتم استبعاد العناصر المكتوب في خانة الصور لها "لا توجد صورة" أو ما شابه</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>إجراءات التصفية بالصور:</label>
                                    <button id="applyImageFilterBtn" class="btn btn-outline-warning btn-sm d-block">
                                        <i class="fas fa-filter"></i> تطبيق فلتر الصور
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Combined Filter Actions -->
                    <div class="filter-card mb-3 collapsible-section">
                        <div class="collapsible-header" onclick="toggleSection('filterActionsSection')">
                            <h5><i class="fas fa-cogs"></i> إجراءات الفلترة</h5>
                            <i class="fas fa-chevron-down toggle-icon" id="filterActionsSection-icon"></i>
                        </div>
                        <div class="collapsible-content" id="filterActionsSection-content" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="btn-group-vertical d-grid gap-2">
                                        <button id="resetFiltersBtn" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-undo"></i> إعادة تعيين جميع الفلاتر
                                        </button>
                                        <button id="showAllResultsBtn" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-list"></i> عرض جميع النتائج
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="btn-group-vertical d-grid gap-2">
                                        <button id="showExcludedBtn" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-eye-slash"></i> عرض المستبعدة
                                        </button>
                                        <button id="showIncludedBtn" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i> عرض المختارة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- General Statistics -->
                    <div id="generalStatsInfo" class="alert alert-success mt-3" style="display: none;">
                        <h5><i class="fas fa-chart-pie"></i> الإحصائيات العامة</h5>
                        <div class="row text-center mb-3">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body p-3">
                                        <h6 class="card-title"><i class="fas fa-database"></i> إجمالي المستخرج</h6>
                                        <h3><span id="totalExtractedCount" class="badge bg-success fs-4">0</span></h3>
                                        <small class="text-muted">العدد الكامل المستخرج</small>
                                        <div class="mt-2">
                                            <button id="exportTotalCSVBtn" class="btn btn-success btn-sm me-1">
                                                <i class="fas fa-file-csv"></i> CSV
                                            </button>
                                            <button id="exportTotalJSONBtn" class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-file-code"></i> JSON
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-body p-3">
                                        <h6 class="card-title"><i class="fas fa-eye"></i> المعروضة حالياً</h6>
                                        <h3><span id="currentlyDisplayedCount" class="badge bg-info fs-4">0</span></h3>
                                        <small class="text-muted">بعد تطبيق الفلاتر</small>
                                        <div class="mt-2">
                                            <button id="exportFilteredCSVBtn" class="btn btn-info btn-sm me-1">
                                                <i class="fas fa-file-csv"></i> CSV
                                            </button>
                                            <button id="exportFilteredJSONBtn" class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-file-code"></i> JSON
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Details (only shown when filters are applied) -->
                        <div id="filterDetailsSection" class="row text-center" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-warning p-2">
                                    <strong><i class="fas fa-filter"></i> تفاصيل الفلترة: </strong>
                                    <span class="badge bg-danger me-2">
                                        <i class="fas fa-times"></i> مستبعد: <span id="excludedByFilterCount">0</span>
                                    </span>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> متبقي: <span id="remainingAfterFilterCount">0</span>
                                    </span>
                                    <span class="mx-2">|</span>
                                    <small id="filterInfoText" class="text-muted"></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="resultsTable"></div>
            </div>

            <!-- Export Info -->
            <div id="exportInfo" style="display: none;" class="form-section">
                <h3><i class="fas fa-file-export"></i> معلومات التصدير</h3>
            </div>

            <!-- Debugger Section -->
            <div id="debuggerSection" class="debugger-section" style="display: none;">
                <h3><i class="fas fa-code"></i> مصحح الأخطاء</h3>
                <div id="codePreview"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
