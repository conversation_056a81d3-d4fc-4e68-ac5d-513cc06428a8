<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحديثات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-test-tube"></i> اختبار التحديثات الجديدة</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- اختبار الوضع الافتراضي -->
                        <div class="form-section mb-4">
                            <h3><i class="fas fa-cog"></i> اختبار الوضع الافتراضي</h3>
                            <div class="form-group">
                                <div class="scraping-mode-selector">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="scrapingMode" id="singlePageMode" value="single">
                                        <label class="form-check-label" for="singlePageMode">
                                            <i class="fas fa-file"></i> صفحة منفردة
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="scrapingMode" id="multiPageMode" value="multi" checked>
                                        <label class="form-check-label" for="multiPageMode">
                                            <i class="fas fa-files"></i> صفحات متتالية
                                        </label>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    ✅ الوضع الافتراضي الآن هو "صفحات متتالية"
                                </small>
                            </div>
                        </div>

                        <!-- اختبار الكلمات الافتراضية -->
                        <div class="form-section mb-4">
                            <h3><i class="fas fa-tags"></i> اختبار الكلمات الافتراضية</h3>
                            
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="form-label"><i class="fas fa-tags"></i> الكلمات الافتراضية:</label>
                                    <div class="default-keywords-container-horizontal">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-film" value="فيلم">
                                            <label class="form-check-label" for="keyword-film">
                                                <span class="badge bg-primary">فيلم</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-watch" value="مشاهدة">
                                            <label class="form-check-label" for="keyword-watch">
                                                <span class="badge bg-success">مشاهدة</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-movie" value="movie">
                                            <label class="form-check-label" for="keyword-movie">
                                                <span class="badge bg-info">movie</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-series" value="مسلسل">
                                            <label class="form-check-label" for="keyword-series">
                                                <span class="badge bg-warning">مسلسل</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input default-keyword-checkbox" type="checkbox" id="keyword-episode" value="حلقة">
                                            <label class="form-check-label" for="keyword-episode">
                                                <span class="badge bg-secondary">حلقة</span>
                                            </label>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">✅ اختر الكلمات الافتراضية التي تريد البحث عنها (تخطيط أفقي مضغوط)</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="filterSearchTerm">كلمات البحث الإضافية:</label>
                                        <input type="text" id="filterSearchTerm" class="form-control" placeholder="أدخل كلمات إضافية للبحث (مفصولة بفاصلة)">
                                        <small class="form-text text-muted">مثال: أكشن, كوميديا, رعب</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>اختبار الوظائف:</label>
                                        <div class="btn-group-vertical d-grid gap-2">
                                            <button id="testKeywordsBtn" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-test"></i> اختبار الكلمات
                                            </button>
                                            <button id="resetTestBtn" class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-undo"></i> إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- منطقة النتائج -->
                        <div class="form-section">
                            <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبار</h3>
                            <div id="testResults" class="alert alert-info">
                                <i class="fas fa-info-circle"></i> اختر بعض الكلمات الافتراضية واضغط على "اختبار الكلمات" لرؤية النتائج
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // اختبار الكلمات الافتراضية
        document.getElementById('testKeywordsBtn').addEventListener('click', function() {
            const selectedKeywords = [];
            const checkboxes = document.querySelectorAll('.default-keyword-checkbox:checked');
            checkboxes.forEach(checkbox => {
                selectedKeywords.push(checkbox.value);
            });

            const additionalTerms = document.getElementById('filterSearchTerm').value.trim();
            let allKeywords = [...selectedKeywords];
            
            if (additionalTerms) {
                const additionalKeywords = additionalTerms.split(',').map(term => term.trim()).filter(term => term);
                allKeywords = allKeywords.concat(additionalKeywords);
            }

            const resultsDiv = document.getElementById('testResults');
            
            if (allKeywords.length === 0) {
                resultsDiv.className = 'alert alert-warning';
                resultsDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> لم يتم اختيار أي كلمات للاختبار';
            } else {
                resultsDiv.className = 'alert alert-success';
                resultsDiv.innerHTML = `
                    <i class="fas fa-check-circle"></i> <strong>تم اختيار ${allKeywords.length} كلمة للبحث:</strong><br>
                    <div class="mt-2">
                        ${allKeywords.map(keyword => `<span class="badge bg-primary me-1">${keyword}</span>`).join('')}
                    </div>
                `;
            }
        });

        // إعادة تعيين الاختبار
        document.getElementById('resetTestBtn').addEventListener('click', function() {
            document.getElementById('filterSearchTerm').value = '';
            const checkboxes = document.querySelectorAll('.default-keyword-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                const label = checkbox.nextElementSibling;
                const badge = label.querySelector('.badge');
                if (badge) {
                    badge.style.transform = 'scale(1)';
                    badge.style.boxShadow = 'none';
                }
            });

            const resultsDiv = document.getElementById('testResults');
            resultsDiv.className = 'alert alert-info';
            resultsDiv.innerHTML = '<i class="fas fa-info-circle"></i> تم إعادة تعيين جميع الاختيارات';
        });

        // إضافة تأثيرات بصرية للكلمات الافتراضية
        const checkboxes = document.querySelectorAll('.default-keyword-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const label = this.nextElementSibling;
                const badge = label.querySelector('.badge');
                
                if (this.checked) {
                    badge.style.transform = 'scale(1.1)';
                    badge.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                } else {
                    badge.style.transform = 'scale(1)';
                    badge.style.boxShadow = 'none';
                }
            });
        });

        // رسالة ترحيب
        console.log('✅ تم تحميل صفحة اختبار التحديثات بنجاح');
        console.log('🔧 الوضع الافتراضي: صفحات متتالية');
        console.log('🏷️ الكلمات الافتراضية: فيلم، مشاهدة، movie، مسلسل، حلقة');
    </script>
</body>
</html>
