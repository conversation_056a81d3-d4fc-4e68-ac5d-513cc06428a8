#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from scraper_classes import WebScraper

def debug_soup_parsing(url, site_name):
    """تشخيص تحليل BeautifulSoup"""
    print(f"🔍 تشخيص تحليل {site_name}")
    print(f"🌐 الرابط: {url}")
    print("=" * 60)
    
    try:
        scraper = WebScraper()
        
        # جلب الصفحة مباشرة
        import requests
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        print(f"✅ تم جلب الصفحة ({len(response.content)} بايت)")
        
        # فحص محتوى الصفحة
        print(f"📄 أول 500 حرف من المحتوى:")
        print(response.text[:500])
        print("...")

        # تحليل HTML
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        print(f"✅ تم تحليل HTML")
        
        # فحص الروابط
        all_links = soup.find_all('a', href=True)
        print(f"📊 إجمالي الروابط في soup: {len(all_links)}")
        
        if len(all_links) > 0:
            print(f"\n🔗 أول 5 روابط:")
            for i, link in enumerate(all_links[:5]):
                print(f"  {i+1}. href: {link.get('href', 'لا يوجد')}")
                print(f"     نص: {link.get_text(strip=True)[:50]}...")
        
        # فحص الصور
        all_images = soup.find_all('img')
        print(f"\n📊 إجمالي الصور في soup: {len(all_images)}")
        
        if len(all_images) > 0:
            print(f"\n🖼️ أول 3 صور:")
            for i, img in enumerate(all_images[:3]):
                print(f"  {i+1}. src: {img.get('src', 'لا يوجد')}")
                print(f"     alt: {img.get('alt', 'لا يوجد')}")
        
        # فحص العناصر المحددة
        selectors_to_test = ['.post__image', '.pm-video-thumb', '.thumbnail']
        
        print(f"\n🎯 فحص selectors محددة:")
        for selector in selectors_to_test:
            elements = soup.select(selector)
            print(f"  {selector}: {len(elements)} عنصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    problem_sites = [
        ("https://a.asd.homes/category/foreign-movies-6/page/2/", "موقع ASD Homes"),
        ("https://vid.shahidwbas.tv/categories.php?cat=adult-movies&page=8&order=DESC", "موقع شاهد وبس")
    ]
    
    for url, site_name in problem_sites:
        debug_soup_parsing(url, site_name)
        print("\n" + "=" * 80 + "\n")

if __name__ == "__main__":
    main()
