#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار التطبيق
يحتوي على اختبارات أساسية لوظائف التطبيق
"""

import requests
import json
import time

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:5000"
TEST_URL = "https://httpbin.org/html"  # موقع اختبار بسيط

def test_app_running():
    """اختبار أن التطبيق يعمل"""
    try:
        response = requests.get(BASE_URL)
        print(f"✅ التطبيق يعمل - كود الاستجابة: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ التطبيق لا يعمل: {e}")
        return False

def test_query_parser():
    """اختبار محلل الاستعلامات"""
    from scraper_classes import QueryParser
    
    parser = QueryParser()
    
    # اختبار استعلام بسيط
    query = "SELECT title, link FROM page WHERE class='test'"
    try:
        result = parser.parse_query(query)
        print("✅ محلل الاستعلامات يعمل بشكل صحيح")
        print(f"   الحقول: {result['fields']}")
        print(f"   المحدد: {result['selector']}")
        return True
    except Exception as e:
        print(f"❌ خطأ في محلل الاستعلامات: {e}")
        return False

def test_scraping_api():
    """اختبار API الاستخراج"""
    data = {
        "url": TEST_URL,
        "query": "SELECT title FROM page WHERE tag='h1'",
        "content_type": "all",
        "element_type": "all",
        "max_pages": 1,
        "max_items_per_file": 100
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/scrape", json=data)
        result = response.json()
        
        if result.get('success'):
            print("✅ API الاستخراج يعمل بشكل صحيح")
            print(f"   عدد العناصر المستخرجة: {result.get('total_items', 0)}")
            return True
        else:
            print(f"❌ خطأ في API الاستخراج: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API الاستخراج: {e}")
        return False

def test_debug_api():
    """اختبار API التصحيح"""
    data = {
        "url": TEST_URL,
        "query": "SELECT title FROM page WHERE tag='h1'"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/debug", json=data)
        result = response.json()
        
        if result.get('success'):
            print("✅ API التصحيح يعمل بشكل صحيح")
            print(f"   عدد العناصر المطابقة: {result['debug_info'].get('matched_elements', 0)}")
            return True
        else:
            print(f"❌ خطأ في API التصحيح: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API التصحيح: {e}")
        return False

def test_page_pattern_analyzer():
    """اختبار محلل أنماط الصفحات"""
    data = {
        "pages": [
            "https://example.com/page/1",
            "https://example.com/page/2",
            "https://example.com/page/3"
        ]
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze_pages", json=data)
        result = response.json()
        
        if result.get('success'):
            print("✅ محلل أنماط الصفحات يعمل بشكل صحيح")
            pattern = result.get('pattern', {})
            print(f"   النمط: {pattern.get('pattern')}")
            print(f"   الخطوة: {pattern.get('step')}")
            return True
        else:
            print(f"❌ خطأ في محلل أنماط الصفحات: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بمحلل أنماط الصفحات: {e}")
        return False

def test_export_api():
    """اختبار API التصدير"""
    # بيانات تجريبية للتصدير
    test_results = [
        {
            "title": "فيلم تجريبي 1",
            "link": "https://example.com/movie1",
            "image": "https://example.com/image1.jpg",
            "type": "movie"
        },
        {
            "title": "فيلم تجريبي 2",
            "link": "https://example.com/movie2",
            "image": "https://example.com/image2.jpg",
            "type": "movie"
        }
    ]
    
    data = {
        "results": test_results,
        "content_type": "movies",
        "max_items_per_file": 100
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/export", json=data)
        result = response.json()
        
        if result.get('success'):
            print("✅ API التصدير يعمل بشكل صحيح")
            print(f"   عدد الملفات المصدرة: {len(result.get('files', []))}")
            return True
        else:
            print(f"❌ خطأ في API التصدير: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API التصدير: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار التطبيق...")
    print("=" * 50)
    
    tests = [
        ("اختبار تشغيل التطبيق", test_app_running),
        ("اختبار محلل الاستعلامات", test_query_parser),
        ("اختبار API الاستخراج", test_scraping_api),
        ("اختبار API التصحيح", test_debug_api),
        ("اختبار محلل أنماط الصفحات", test_page_pattern_analyzer),
        ("اختبار API التصدير", test_export_api)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        time.sleep(1)  # انتظار قصير بين الاختبارات
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
