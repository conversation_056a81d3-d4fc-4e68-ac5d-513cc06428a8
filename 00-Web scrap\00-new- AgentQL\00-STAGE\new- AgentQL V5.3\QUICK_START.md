# 🚀 دليل البدء السريع - أداة استخراج بيانات الأفلام (محدث)

## ⚡ التشغيل السريع

### 1. تشغيل التطبيق
```bash
python app.py
```

### 2. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

---

## 🆕 الميزات الجديدة - نظرة سريعة

### 🎛️ التحكم في العمليات (جديد!)
- **⏸️ إيقاف مؤقت**: توقف مؤقت مع إمكانية الاستئناف
- **▶️ استئناف**: متابعة العملية من حيث توقفت
- **🛑 إيقاف وتصدير**: إيقاف نهائي مع حفظ النتائج الحالية

### 🔍 تصفية النتائج (جديد!)
- **استبعاد الأفلام**: إزالة الأفلام المحتوية على كلمة معينة
- **اختيار الأفلام**: عرض الأفلام المحتوية على كلمة معينة فقط
- **إحصائيات مفصلة**: عرض تفصيلي لنتائج التصفية

---

## 🎬 كيفية استخراج بيانات الأفلام

### الخطوة 1: أدخل رابط موقع الأفلام
```
https://movies-site.com/movies
```

### الخطوة 2: استخدم الاستعلام الافتراضي
الاستعلام مُعد مسبقاً:
```sql
SELECT title, link, image FROM page WHERE class="movie-card"
```

**هذا الاستعلام يبحث عن:**
- `title` = اسم الفيلم
- `link` = رابط الفيلم  
- `image` = صورة الفيلم

### الخطوة 3: اضغط "بدء الاستخراج"

---

## 🔧 إذا لم تظهر نتائج

### جرب هذه الاستعلامات البديلة:

```sql
SELECT title, link, image FROM page WHERE class="movie"
```

```sql
SELECT title, link, image FROM page WHERE class="film"
```

```sql
SELECT title, link, image FROM page WHERE class="item"
```

```sql
SELECT title, link, image FROM page WHERE tag="article"
```

---

## 🛠️ استخدام مصحح الأخطاء

1. أدخل رابط الموقع
2. أدخل الاستعلام
3. اضغط **"تصحيح الاستعلام"**
4. ستظهر لك العناصر الموجودة في الصفحة
5. عدّل الاستعلام حسب ما تراه

---

## 📊 تصدير النتائج

بعد الحصول على النتائج:
1. اضغط **"تصدير النتائج"**
2. سيتم إنشاء ملف JSON يحتوي على:

```json
{
  "movies_info": [
    {
      "movies_name": "اسم الفيلم",
      "movies_img": "رابط صورة الفيلم", 
      "movies_href": "رابط الفيلم"
    }
  ]
}
```

---

## 🎯 نصائح للحصول على أفضل النتائج

### 1. افحص الموقع أولاً
- افتح الموقع في متصفح منفصل
- اضغط F12 لفتح أدوات المطور
- ابحث عن العناصر التي تحتوي على الأفلام

### 2. ابدأ بالاستعلام الافتراضي
```sql
SELECT title, link, image FROM page WHERE class="movie-card"
```

### 3. استخدم مصحح الأخطاء
- يساعدك على فهم بنية الصفحة
- يعرض العناصر الموجودة
- يوضح ما يطابق استعلامك

### 4. جرب class مختلفة
إذا لم يعمل `movie-card`، جرب:
- `movie-item`
- `film-card`
- `movie`
- `item`
- `card`

---

## ⚠️ مشاكل شائعة وحلولها

### المشكلة: لا توجد نتائج
**الحل:**
1. تأكد من صحة رابط الموقع
2. استخدم مصحح الأخطاء
3. جرب استعلام أبسط: `SELECT title FROM page WHERE tag="div"`

### المشكلة: نتائج خاطئة
**الحل:**
1. كن أكثر تحديداً في الـ class
2. استخدم مصحح الأخطاء لفهم البنية
3. جرب class أو tag مختلف

### المشكلة: صور لا تظهر
**الحل:**
1. تأكد من أن الموقع يحتوي على صور
2. جرب استعلام: `SELECT image FROM page WHERE tag="img"`
3. بعض المواقع تستخدم lazy loading

---

## 📱 أمثلة سريعة

### موقع أفلام عادي
```
الرابط: https://movies-site.com
الاستعلام: SELECT title, link, image FROM page WHERE class="movie-card"
```

### موقع تورنت
```
الرابط: https://torrent-site.com/movies
الاستعلام: SELECT title, link, image FROM page WHERE class="torrent-item"
```

### موقع تحميل
```
الرابط: https://download-site.com/movies
الاستعلام: SELECT title, link, image FROM page WHERE class="download-box"
```

---

---

## 🆕 استخدام الميزات الجديدة - خطوات سريعة

### 🎛️ التحكم في العمليات أثناء الاستخراج
1. ابدأ عملية الاستخراج كالمعتاد
2. ستظهر أزرار التحكم في قسم "سجل التطور اللحظي"
3. استخدم:
   - **⏸️ إيقاف مؤقت** للتوقف المؤقت
   - **▶️ استئناف** للمتابعة
   - **🛑 إيقاف وتصدير** للإيقاف مع حفظ النتائج

### 🔍 تصفية النتائج بعد الاستخراج
1. بعد الحصول على النتائج، انتقل لقسم "النتائج"
2. في قسم "تصفية وتحليل النتائج":
   - اكتب كلمة البحث (مثل: "أكشن", "كوميديا", "رعب")
   - اضغط **"استبعاد"** لإزالة الأفلام المحتوية على الكلمة
   - أو اضغط **"اختيار"** لعرض الأفلام المحتوية على الكلمة فقط
3. استخدم أزرار العرض للتنقل بين النتائج
4. اضغط **"تصدير المصفاة"** لحفظ النتائج المصفاة

### 🧪 اختبار سريع للميزات الجديدة
```bash
python test_new_features.py
```

---

## 🎉 مبروك!

الآن أنت جاهز لاستخراج وإدارة بيانات الأفلام بطريقة احترافية!

**تذكر:**
- ابدأ بالاستعلام الافتراضي
- استخدم مصحح الأخطاء إذا احتجت مساعدة
- **🆕 جرب الميزات الجديدة للتحكم والتصفية**

---

**للمزيد من التفاصيل، راجع:**
- `README.md` - دليل شامل
- `USAGE.md` - دليل الاستخدام المفصل
- `movie_queries_examples.md` - أمثلة على الاستعلامات
- **🆕 `NEW_FEATURES_GUIDE.md` - دليل الميزات الجديدة**
- **🆕 `FEATURES_SUMMARY.md` - ملخص شامل للتحديثات**
