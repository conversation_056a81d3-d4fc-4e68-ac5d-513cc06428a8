<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح استخراج الروابط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .result-box {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        
        .error-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .btn-test {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
            color: white;
        }
        
        .link-analysis {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .priority-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .priority-high { background: #d4edda; color: #155724; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #f8d7da; color: #721c24; }
        
        .test-title {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-link text-primary"></i>
                اختبار إصلاح استخراج الروابط
            </h1>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>الهدف:</strong> اختبار الحلول الجديدة لاستخراج روابط الأفلام الصحيحة وتجنب الروابط الخاطئة
            </div>

            <!-- اختبار فلترة الروابط -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-filter"></i>
                    اختبار فلترة الروابط
                </h3>
                
                <p class="text-muted">اختبار الروابط المختلفة لمعرفة أيها سيتم قبوله أو رفضه:</p>
                
                <div class="row mb-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="testLink" 
                               placeholder="أدخل رابط للاختبار">
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-test w-100" onclick="testLinkFilter()">
                            <i class="fas fa-check"></i> اختبار الفلترة
                        </button>
                    </div>
                </div>
                
                <div id="filterResults"></div>
            </div>

            <!-- اختبار نظام الأولوية -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-sort-amount-up"></i>
                    اختبار نظام الأولوية
                </h3>
                
                <p class="text-muted">اختبار ترتيب الروابط حسب الأولوية:</p>
                
                <div class="mb-3">
                    <textarea class="form-control" id="linksList" rows="5" 
                              placeholder="أدخل قائمة الروابط (رابط واحد في كل سطر)"></textarea>
                </div>
                
                <button class="btn btn-test" onclick="testPrioritySystem()">
                    <i class="fas fa-sort"></i> ترتيب حسب الأولوية
                </button>
                
                <div id="priorityResults"></div>
            </div>

            <!-- اختبار روابط شائعة -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-list"></i>
                    اختبار روابط شائعة
                </h3>
                
                <p class="text-muted">اختبار سريع لأنواع مختلفة من الروابط:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-test w-100 mb-2" onclick="testCommonLinks('good')">
                            <i class="fas fa-thumbs-up"></i> روابط جيدة
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-test w-100 mb-2" onclick="testCommonLinks('bad')">
                            <i class="fas fa-thumbs-down"></i> روابط سيئة
                        </button>
                    </div>
                </div>
                
                <div id="commonLinksResults"></div>
            </div>

            <!-- اختبار شامل -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-cogs"></i>
                    اختبار شامل للنظام
                </h3>
                
                <button class="btn btn-test btn-lg" onclick="runComprehensiveTest()">
                    <i class="fas fa-play"></i> تشغيل الاختبار الشامل
                </button>
                
                <div id="comprehensiveResults"></div>
            </div>

            <!-- نتائج المقارنة -->
            <div class="test-section">
                <h3 class="test-title">
                    <i class="fas fa-balance-scale"></i>
                    مقارنة النتائج
                </h3>
                
                <div id="comparisonResults">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>قبل الإصلاح:</h5>
                            <div class="code-block">
                                https://vid.shahidwbas.tv/categories.php?cat=adult-movies&page=5&order=DESC#modal-login-form
                            </div>
                            <span class="badge bg-danger">رابط خاطئ</span>
                        </div>
                        <div class="col-md-6">
                            <h5>بعد الإصلاح:</h5>
                            <div class="code-block">
                                https://vid.shahidwbas.tv/watch.php?vid=581f96851
                            </div>
                            <span class="badge bg-success">رابط صحيح</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // نسخ وظائف الفلترة من الكود المحسن
        const excludedPatterns = [
            '#modal-login-form',
            'categories.php',
            'page=',
            'order=',
            'javascript:',
            'mailto:',
            '#',
            'login',
            'register',
            'signup'
        ];
        
        const preferredKeywords = ['watch', 'view', 'play', 'movie', 'film', 'video', 'vid='];
        
        function isValidLink(href) {
            if (!href || href === '#') {
                return false;
            }
            
            const hrefLower = href.toLowerCase();
            
            for (const pattern of excludedPatterns) {
                if (hrefLower.includes(pattern)) {
                    return false;
                }
            }
            
            return true;
        }
        
        function getLinkPriority(href) {
            if (!href) return 0;
            
            const hrefLower = href.toLowerCase();
            let priority = 1;
            
            // أولوية عالية للكلمات المفتاحية
            for (const keyword of preferredKeywords) {
                if (hrefLower.includes(keyword)) {
                    priority += 10;
                    break;
                }
            }
            
            // أولوية إضافية لمعرف الفيديو
            if (hrefLower.includes('vid=') || hrefLower.includes('id=')) {
                priority += 5;
            }
            
            return priority;
        }
        
        function getPriorityClass(priority) {
            if (priority >= 15) return 'priority-high';
            if (priority >= 5) return 'priority-medium';
            return 'priority-low';
        }
        
        function getPriorityText(priority) {
            if (priority >= 15) return 'عالية';
            if (priority >= 5) return 'متوسطة';
            return 'منخفضة';
        }

        function testLinkFilter() {
            const link = document.getElementById('testLink').value.trim();
            const resultDiv = document.getElementById('filterResults');
            
            if (!link) {
                resultDiv.innerHTML = '<div class="error-box">يرجى إدخال رابط للاختبار</div>';
                return;
            }
            
            const isValid = isValidLink(link);
            const priority = getLinkPriority(link);
            const priorityClass = getPriorityClass(priority);
            const priorityText = getPriorityText(priority);
            
            let resultClass = isValid ? 'result-box' : 'error-box';
            let statusText = isValid ? 'مقبول' : 'مرفوض';
            let statusIcon = isValid ? '✅' : '❌';
            
            resultDiv.innerHTML = `
                <div class="${resultClass}">
                    <strong>الرابط:</strong> ${link}<br>
                    <strong>الحالة:</strong> ${statusIcon} ${statusText}<br>
                    <strong>النقاط:</strong> ${priority}
                    <span class="priority-badge ${priorityClass}">أولوية ${priorityText}</span>
                </div>
            `;
        }

        function testPrioritySystem() {
            const linksList = document.getElementById('linksList').value.trim();
            const resultDiv = document.getElementById('priorityResults');
            
            if (!linksList) {
                resultDiv.innerHTML = '<div class="error-box">يرجى إدخال قائمة الروابط</div>';
                return;
            }
            
            const links = linksList.split('\n').filter(link => link.trim());
            const linksWithPriority = [];
            
            links.forEach(link => {
                const trimmedLink = link.trim();
                if (trimmedLink) {
                    const isValid = isValidLink(trimmedLink);
                    const priority = getLinkPriority(trimmedLink);
                    linksWithPriority.push({ link: trimmedLink, priority, isValid });
                }
            });
            
            // ترتيب حسب الأولوية
            linksWithPriority.sort((a, b) => b.priority - a.priority);
            
            let results = '<h5>الروابط مرتبة حسب الأولوية:</h5>';
            
            linksWithPriority.forEach((item, index) => {
                const priorityClass = getPriorityClass(item.priority);
                const priorityText = getPriorityText(item.priority);
                const statusIcon = item.isValid ? '✅' : '❌';
                const statusText = item.isValid ? 'مقبول' : 'مرفوض';
                
                results += `
                    <div class="link-analysis">
                        <strong>${index + 1}.</strong> ${item.link}<br>
                        <small>
                            ${statusIcon} ${statusText} | 
                            النقاط: ${item.priority}
                            <span class="priority-badge ${priorityClass}">أولوية ${priorityText}</span>
                        </small>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = results;
        }

        function testCommonLinks(type) {
            const resultDiv = document.getElementById('commonLinksResults');
            
            let testLinks = [];
            
            if (type === 'good') {
                testLinks = [
                    'https://vid.shahidwbas.tv/watch.php?vid=581f96851',
                    'https://example.com/view.php?id=123',
                    'https://site.com/play/movie/456',
                    'https://movies.com/film.php?movie_id=789'
                ];
            } else {
                testLinks = [
                    'https://vid.shahidwbas.tv/categories.php?cat=adult-movies&page=5&order=DESC#modal-login-form',
                    'https://site.com/login.php',
                    'https://example.com/register',
                    'javascript:void(0)',
                    '#modal-login-form'
                ];
            }
            
            let results = `<h5>اختبار الروابط ${type === 'good' ? 'الجيدة' : 'السيئة'}:</h5>`;
            
            testLinks.forEach((link, index) => {
                const isValid = isValidLink(link);
                const priority = getLinkPriority(link);
                const priorityClass = getPriorityClass(priority);
                const priorityText = getPriorityText(priority);
                const statusIcon = isValid ? '✅' : '❌';
                const statusText = isValid ? 'مقبول' : 'مرفوض';
                
                results += `
                    <div class="link-analysis">
                        <strong>${index + 1}.</strong> ${link}<br>
                        <small>
                            ${statusIcon} ${statusText} | 
                            النقاط: ${priority}
                            <span class="priority-badge ${priorityClass}">أولوية ${priorityText}</span>
                        </small>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = results;
        }

        function runComprehensiveTest() {
            const resultDiv = document.getElementById('comprehensiveResults');
            resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تشغيل الاختبار الشامل...</div>';
            
            setTimeout(() => {
                const testCases = [
                    // حالات إيجابية
                    { link: 'https://vid.shahidwbas.tv/watch.php?vid=581f96851', expected: true, description: 'رابط مشاهدة صحيح' },
                    { link: 'https://example.com/view.php?id=123', expected: true, description: 'رابط عرض صحيح' },
                    { link: 'https://site.com/play/movie/456', expected: true, description: 'رابط تشغيل صحيح' },
                    
                    // حالات سلبية
                    { link: 'https://vid.shahidwbas.tv/categories.php?page=5#modal-login-form', expected: false, description: 'رابط تسجيل دخول' },
                    { link: 'https://site.com/categories.php?cat=movies&page=2', expected: false, description: 'رابط تصفح' },
                    { link: 'javascript:void(0)', expected: false, description: 'رابط جافاسكريبت' },
                    { link: '#modal-login-form', expected: false, description: 'رابط مودال' },
                    { link: '', expected: false, description: 'رابط فارغ' }
                ];
                
                let passedTests = 0;
                let results = '<h5>نتائج الاختبار الشامل:</h5>';
                
                testCases.forEach((testCase, index) => {
                    const actualResult = isValidLink(testCase.link);
                    const passed = actualResult === testCase.expected;
                    const priority = getLinkPriority(testCase.link);
                    
                    if (passed) passedTests++;
                    
                    results += `
                        <div class="${passed ? 'result-box' : 'error-box'}">
                            <strong>اختبار ${index + 1}:</strong> ${testCase.description}<br>
                            <strong>الرابط:</strong> ${testCase.link || 'فارغ'}<br>
                            <strong>المتوقع:</strong> ${testCase.expected ? 'مقبول' : 'مرفوض'}<br>
                            <strong>الفعلي:</strong> ${actualResult ? 'مقبول' : 'مرفوض'}<br>
                            <strong>النقاط:</strong> ${priority}<br>
                            <span class="badge ${passed ? 'bg-success' : 'bg-danger'}">
                                ${passed ? 'نجح ✅' : 'فشل ❌'}
                            </span>
                        </div>
                    `;
                });
                
                results += `
                    <div class="alert alert-info mt-3">
                        <strong>ملخص النتائج:</strong> نجح ${passedTests} من ${testCases.length} اختبار
                        <div class="progress mt-2">
                            <div class="progress-bar ${passedTests === testCases.length ? 'bg-success' : 'bg-warning'}" 
                                 style="width: ${(passedTests / testCases.length) * 100}%">
                                ${Math.round((passedTests / testCases.length) * 100)}%
                            </div>
                        </div>
                    </div>
                `;
                
                resultDiv.innerHTML = results;
            }, 1000);
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            document.getElementById('testLink').value = 'https://vid.shahidwbas.tv/watch.php?vid=581f96851';
            
            document.getElementById('linksList').value = `https://vid.shahidwbas.tv/watch.php?vid=581f96851
https://vid.shahidwbas.tv/categories.php?page=5#modal-login-form
https://example.com/view.php?id=123
https://site.com/login.php
https://movies.com/play/film/456`;
        };
    </script>
</body>
</html>
