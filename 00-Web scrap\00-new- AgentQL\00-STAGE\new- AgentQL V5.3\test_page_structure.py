#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import sys

def analyze_page_structure(url):
    """تحليل بنية صفحة الويب"""
    try:
        print(f"🔍 فحص الصفحة: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print(f"✅ تم جلب الصفحة بنجاح ({len(response.content)} بايت)")
        
        # البحث عن الروابط التي تحتوي على صور
        links_with_images = soup.find_all('a', href=True)
        image_links = [link for link in links_with_images if link.find('img')]
        print(f"📊 عدد الروابط التي تحتوي على صور: {len(image_links)}")
        
        if image_links:
            print("\n🎬 أول 5 روابط مع صور:")
            for i, link in enumerate(image_links[:5]):
                print(f"\n--- رابط {i+1} ---")
                href = link.get('href', 'لا يوجد')
                print(f"href: {href}")
                
                img = link.find('img')
                if img:
                    print(f"صورة src: {img.get('src', 'لا يوجد')}")
                    print(f"صورة alt: {img.get('alt', 'لا يوجد')}")
                    print(f"صورة title: {img.get('title', 'لا يوجد')}")
                
                text = link.get_text(strip=True)
                if text:
                    print(f"نص الرابط: {text[:100]}...")
                else:
                    print("نص الرابط: لا يوجد")
        
        # البحث عن عناصر أخرى محتملة
        divs = soup.find_all('div', class_=True)
        print(f"\n📦 عدد divs مع classes: {len(divs)}")
        
        # عرض بعض الـ classes الشائعة
        classes = {}
        for div in divs:
            for cls in div.get('class', []):
                classes[cls] = classes.get(cls, 0) + 1
        
        print("\n🏷️ أكثر الـ classes شيوعاً:")
        for cls, count in sorted(classes.items(), key=lambda x: x[1], reverse=True)[:15]:
            print(f"  {cls}: {count}")
        
        # البحث عن عناصر محتملة للأفلام
        print("\n🎭 البحث عن عناصر الأفلام المحتملة:")
        
        # البحث في العناوين
        titles = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        print(f"العناوين: {len(titles)}")
        
        # البحث في الصور
        all_images = soup.find_all('img', src=True)
        print(f"الصور: {len(all_images)}")
        
        # البحث في الروابط
        all_links = soup.find_all('a', href=True)
        print(f"الروابط: {len(all_links)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحليل الصفحة: {e}")
        return False

if __name__ == "__main__":
    # المواقع المطلوب اختبارها
    test_urls = [
        "https://a.asd.homes/category/foreign-movies-6/page/2/",
        "https://mc.turkishasq.com/category.php?cat=movies",
        "https://vid.shahidwbas.tv/categories.php?cat=adult-movies&page=8&order=DESC"
    ]
    
    for url in test_urls:
        print("=" * 80)
        analyze_page_structure(url)
        print("\n")
