# تحديث نظام الاستراتيجيات - Strategy System Update

## 🎯 نظرة عامة

تم تطوير نظام إدارة الاستراتيجيات الجديد لتحسين قدرة التطبيق على التعامل مع مختلف أنواع المواقع، بما في ذلك إضافة استراتيجية مخصصة لمواقع مثل `aflam.top`.

## 🆕 الميزات الجديدة

### 1. نظام إدارة الاستراتيجيات (Strategy Manager)
- **الوضع التلقائي**: يجرب جميع الاستراتيجيات المتاحة ويختار الأفضل تلقائياً
- **الوضع اليدوي**: يمكن اختيار استراتيجية محددة يدوياً
- **إدارة مرنة**: يمكن إضافة أو تعطيل استراتيجيات بسهولة

### 2. الاستراتيجيات المتاحة

#### الاستراتيجية 1: بطاقات الأفلام التقليدية (Movie Cards Strategy)
- **الوصف**: تتعامل مع المواقع التي تستخدم بطاقات الأفلام التقليدية
- **مناسبة لـ**: المواقع العامة للأفلام والمسلسلات
- **Selectors**: `.movie-card`, `.film-item`, `.post__image`, إلخ

#### الاستراتيجية 2: القوائم والجداول (List Based Strategy)
- **الوصف**: تتعامل مع المواقع التي تعرض المحتوى في قوائم أو جداول
- **مناسبة لـ**: المواقع التي تستخدم تخطيط القوائم
- **Selectors**: `table`, `ul`, `ol`, `.list-item`, إلخ

#### الاستراتيجية 3: مواقع aflam.top (Aflam Top Strategy) - جديد! 🆕
- **الوصف**: استراتيجية مخصصة لمواقع مثل aflam.top وما شابهها
- **مناسبة لـ**: المواقع العربية للأفلام مع تخطيط مشابه لـ aflam.top
- **ميزات خاصة**:
  - دعم محسن للمحتوى العربي
  - استخراج متقدم للعناوين والروابط والصور
  - تحديد ذكي لنوع المحتوى (فيلم/مسلسل)
  - معالجة خاصة للصور المؤجلة (lazy loading)

## 🔧 كيفية الاستخدام

### من خلال الواجهة الرسومية

1. **افتح التطبيق** في المتصفح
2. **اختر الاستراتيجية** من القائمة المنسدلة في قسم "إدارة الاستراتيجيات":
   - `تلقائي`: جرب جميع الاستراتيجيات
   - `استراتيجية 1`: بطاقات الأفلام التقليدية
   - `استراتيجية 2`: القوائم والجداول
   - `استراتيجية 3`: مواقع aflam.top
3. **أدخل الرابط والاستعلام** كالمعتاد
4. **ابدأ الاستخراج**

### من خلال API

#### الحصول على معلومات الاستراتيجيات
```http
GET /api/strategies
```

#### تعيين استراتيجية محددة
```http
POST /api/set_strategy
Content-Type: application/json

{
    "strategy_id": 3  // أو "auto" للوضع التلقائي
}
```

### من خلال Python

```python
from scraper_classes import WebScraper

# إنشاء كائن الاستخراج
scraper = WebScraper()

# تعيين استراتيجية محددة
scraper.set_strategy(3)  # استراتيجية aflam.top

# أو تفعيل الوضع التلقائي
scraper.enable_auto_strategy()

# الحصول على معلومات الاستراتيجيات
info = scraper.get_current_strategy_mode()
print(info)
```

## 🧪 الاختبار

تم إنشاء ملف اختبار شامل `test_aflam_strategy.py` لاختبار النظام الجديد:

```bash
python test_aflam_strategy.py
```

الاختبار يشمل:
- اختبار التبديل بين الاستراتيجيات
- اختبار الاستراتيجية الجديدة مع موقع aflam.top
- اختبار الوضع التلقائي والوضع اليدوي

## 📊 مثال على الاستخدام مع aflam.top

```python
# تعيين استراتيجية aflam.top
scraper.set_strategy(3)

# استخراج من صفحة أفلام الأكشن
url = "https://aflam.top/genre/%d8%a7%d9%83%d8%b4%d9%86/?page=2"
results = scraper.scrape_website(url, "أفلام", max_pages=1)

# النتائج ستحتوي على:
# - العنوان (title)
# - الرابط (link) 
# - الصورة (image)
# - النوع (type: movie/series)
```

## 🔄 التوافق مع الإصدارات السابقة

- جميع الميزات السابقة تعمل بنفس الطريقة
- الوضع التلقائي مفعل افتراضياً
- لا حاجة لتغيير الكود الموجود

## 🚀 إضافة استراتيجيات جديدة

لإضافة استراتيجية جديدة:

1. **إنشاء كلاس جديد** يرث من `ScrapingStrategy`
2. **تنفيذ دالة** `extract_data()`
3. **إضافة الاستراتيجية** إلى `StrategyManager`

```python
class MyCustomStrategy(ScrapingStrategy):
    def __init__(self):
        super().__init__("My Strategy", "وصف الاستراتيجية")
    
    def extract_data(self, soup, base_url):
        # منطق الاستخراج هنا
        return results
```

## 📈 تحسينات الأداء

- **استخراج أسرع**: كل استراتيجية محسنة لنوع معين من المواقع
- **دقة أعلى**: استراتيجيات مخصصة تعطي نتائج أفضل
- **مرونة أكبر**: يمكن التحكم في الاستراتيجية حسب الحاجة

## 🐛 إصلاح المشاكل

### المشكلة: لا تظهر النتائج من موقع aflam.top
**الحل**: تأكد من تعيين الاستراتيجية 3 أو استخدام الوضع التلقائي

### المشكلة: خطأ في تحميل الاستراتيجيات
**الحل**: تأكد من تشغيل الخادم وإعادة تحميل الصفحة

### المشكلة: النتائج غير دقيقة
**الحل**: جرب استراتيجية مختلفة أو استخدم الوضع التلقائي

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات لاستراتيجيات جديدة، يرجى:
1. فحص ملف الاختبار `test_aflam_strategy.py`
2. مراجعة سجلات الأخطاء في وحدة التحكم
3. تجربة الوضع التلقائي أولاً

---

**تم التطوير بواسطة**: نظام الاستراتيجيات المتقدم  
**التاريخ**: 2025-01-22  
**الإصدار**: 5.3 مع نظام الاستراتيجيات
